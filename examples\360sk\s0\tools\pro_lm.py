# encoding=utf-8
from tqdm import tqdm
import jieba
from g2pM import G2pM
import paddle
model = G2pM()
paddle.enable_static()
jieba.enable_paddle()

text_f = open("data/test_cuishou/text","r")
trans_f = open("data/test_cuishou/trans.txt","w")
phones_f = open("data/test_cuishou/lexicon.txt","w")
lexions = set()
texts = text_f.readlines()

for i in tqdm(range(len(texts))):
    try:
        ids, txt = texts[i].strip().split(" ",1)
        seg_list = jieba.cut(txt)  # 默认是精确模式
        # print(" ".join(seg_list))
        sent = (" ".join(seg_list))
        trans_f.write(ids+" "+sent+"\n")
        for word in sent.split(" "):
            g2m_res = model(word, tone=True, char_split=False)
            lexions.add(word+" "+" ".join(g2m_res))

    except:
        print(texts[i].strip())

for element in lexions:
    phones_f.write(element+"\n")


