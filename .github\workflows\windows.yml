name: Build Windows Runtime

on:
  workflow_dispatch:

env:
  RUNTIME_DIR: runtime/libtorch

jobs:
  build:
    runs-on: windows-latest
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/checkout@v3
      - name: Build
        run: |
          cd ${{ env.RUNTIME_DIR }}
          cmake -B build -DGRAPH_TOOLS=ON -DCMAKE_BUILD_TYPE=Release -DBUILD_TESTING=OFF
          cmake --build build --config Release -j$(nproc)

      - name: Prepare Release Binary
        run: |
          cd ${{ env.RUNTIME_DIR }}
          mkdir -p wenet/kaldi
          cp build/*.dll wenet
          cp build/api/Release/*.dll wenet
          cp build/bin/Release/*.exe wenet
          cp build/kaldi/Release/*.exe wenet/kaldi

      - name: Upload WeNet Binary
        uses: actions/upload-artifact@v3
        with:
          name: release-wenet-binary
          path: ${{ env.RUNTIME_DIR }}/wenet
