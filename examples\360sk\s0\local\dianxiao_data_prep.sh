#!/bin/bash

# Copyright 2019 Xingyu Na
# Apache 2.0

. ./path.sh || exit 1;

if [ $# != 2 ]; then
  echo "Usage: $0 <corpus-path> <data-path>"
  echo " $0 /export/a05/xna/data/data_dianxiao data/dianxiao"
  exit 1;
fi


dianxiao_audio_dir=$1/corpus
dianxiao_text=$1/transcript/transcript.txt
data=$2

train_dir=$data/local/train
dev_dir=$data/local/dev
test_dir=$data/local/test
tmp_dir=$data/local/tmp

mkdir -p $train_dir
mkdir -p $dev_dir
mkdir -p $test_dir
mkdir -p $tmp_dir

# data directory check
if [ ! -d $dianxiao_audio_dir ] || [ ! -f $dianxiao_text ]; then
  echo "Error: $0 requires two directory arguments"
  exit 1;
fi

echo "**** Creating dianxiao data folder ****"

# find wav audio file for train dev test
find $dianxiao_audio_dir -iname "*.wav"  > $tmp_dir/wav.flist

grep -i "corpus/train"  $tmp_dir/wav.flist > $train_dir/wav.flist || exit 1;
grep -i "corpus/dev"   $tmp_dir/wav.flist > $dev_dir/wav.flist || exit 1;
grep -i "corpus/test"   $tmp_dir/wav.flist > $test_dir/wav.flist || exit 1;

rm -r $tmp_dir

# Preparation

for dir in $train_dir $dev_dir $test_dir; do

  echo "Preparing $dir wav.scp text utt2spk spk2utt"
  # Extract utt.list , spk.list
  sed -e 's/\.wav//' $dir/wav.flist | awk -F '/' '{print $NF}'  > $dir/utt.list
  sed -e 's/\.wav//' $dir/wav.flist | awk -F '/' '{i=NF-1;printf("%s\n",$i)}' > $dir/spk.list

  # Paste utt with spk, wav
  paste -d' ' $dir/utt.list $dir/spk.list   > $dir/utt2spk_tmp
  paste -d' ' $dir/utt.list $dir/wav.flist > $dir/wav.scp_tmp

  # sort utt2spk, wav.scp
  tools/filter_scp.pl -f 1 $dir/utt.list $dir/utt2spk_tmp | sort -u | awk '{print $1" "$2}' > $dir/utt2spk
  tools/filter_scp.pl -f 1 $dir/utt.list $dir/wav.scp_tmp | sort -u > $dir/wav.scp

  # generate text
  tools/filter_scp.pl -f 1 $dir/utt.list $dianxiao_text  > $dir/transcripts.txt
  cat $dir/transcripts.txt |\
    sed 's/（//g' | sed 's/）//g' |\
    sed 's/！//g' | sed 's/？//g' |\
    sed 's/，//g' | sed 's/－//g' |\
    sed 's/：//g' | sed 's/；//g' |\
    sed 's/　//g' | sed 's/。//g' |\
    sed 's/`//g' | sed 's/,//g' |\
    sed 's/://g' | sed 's/?//g' |\
    sed 's/\///g' | sed 's/·//g' |\
    sed 's/\"//g' | sed 's/“//g' |\
    sed 's/”//g' | sed 's/\\//g' |\
    sed 's/…//g' | sed 's/、//g' |\
    sed 's/《//g' | sed 's/》//g' |\
    sed 's/\[//g' | sed 's/\]//g' | sed 's/FIL//g' | sed 's/SPK//' |sed 's/\.//g' |\
	sed 's/!//g' | sed 's/(//g' |sed 's/)//g' | sed 's/*//g'  | sed 's/;//g' |\
	sed 's/=//g' | sed 's/@//g' |sed 's/~//g' | sed 's/×//g' | sed 's/’//g' | sed 's/」//g'|\
	sed 's/Ａ/A/g' | sed 's/Ｂ/B/g' |sed 's/Ｃ/C/g' | sed 's/Ｃ/C/g' | sed 's/Ｄ/D/g' | sed 's/Ｅ/E/g' |\
	sed 's/Ｆ/F/g' | sed 's/Ｇ/G/g' |sed 's/Ｈ/H/g' | sed 's/Ｋ/K/g' | sed 's/Ｍ/M/g' | sed 's/Ｎ/N/g' |\
	sed 's/Ｐ/P/g' | sed 's/Ｑ/Q/g' |sed 's/Ｒ/R/g' | sed 's/Ｓ/S/g' | sed 's/Ｕ/U/g' | sed 's/Ｘ/X/g' |\
	sed 's/ｂ/b/g' | sed 's/ｅ/e/g' |sed 's/ｇ/g/g' | sed 's/ｊ/j/g' | sed 's/ｉ/i/g' | sed 's/ｎ/n/g' |\
	sed 's/ｏ/o/g' | sed 's/ｐ/p/g' |sed 's/ｒ/r/g' | sed 's/ｓ/s/g' | sed 's/ｕ/u/g' |\
    tr '[a-z]' '[A-Z]'| sort -u  > $dir/text

  # generate spk2utt
  tools/utt2spk_to_spk2utt.pl <$dir/utt2spk > $dir/spk2utt
done

mkdir -p $data/train $data/dev $data/test

for f in spk2utt utt2spk wav.scp text; do
  cp $train_dir/$f $data/train/$f || exit 1;
  cp $dev_dir/$f $data/dev/$f || exit 1;
  cp $test_dir/$f $data/test/$f || exit 1;  
done


tools/fix_data_dir.sh $data/train || exit 1;
tools/fix_data_dir.sh $data/dev || exit 1;
tools/fix_data_dir.sh $data/test || exit 1;

echo "$0: dianxiao data preparation succeeded"
exit 0;

