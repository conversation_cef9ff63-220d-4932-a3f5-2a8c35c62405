#!/bin/bash

# Copyright 2019 Mobvoi Inc. All Rights Reserved.
. ./path.sh || exit 1;

gpuid=1

dir=exp/cuishou_conformer_V3.3
dir=/dat/workdir/chenjinming/01_WENET/02_wenet_code/wenet-20220905/wenet/examples/360sk/s0_cuishou/exp/conformer
dir=/dat/workdir/chenjinming/wenet-main/examples/360sk/s0/exp/u2++_efficonformer_cuishou_finetune_v3
dir=/dat/workdir/chenjinming/wenet-main/examples/360sk/s0/exp/u2++_efficonformer_cuishou_finetune_v6
dir=/data/cjm/asr_tool/wenet-main/examples/360sk/s0/exp/u2++_efficonformer_cuishou_finetune_v6
dir=exp/cuishou_v3.2_fintune
dict=$dir/lang_char.txt

average_checkpoint=true
decode_checkpoint=$dir/avg_5.pt

testdata=/dat/workdir/chenjinming/01_WENET/02_wenet_code/wenet-cjm-newdata/wenet/runtime/libtorch/cut_wavs
testdata=/dat/workdir/chenjinming/01_WENET/03_test_data/cuishou_test_data/asr-sd-0622-300/cutwavs
testdata=/dat/workdir/chenjinming/01_WENET/03_test_data/fangyan_test/wavs
testdata=/dat/workdir/chenjinming/01_WENET/03_test_data/cuishou_test_data
#testdata=/data/cjm/asr_tool/wenet-main/examples/360sk/s0/data/test_cuishou_mini
average_num=5
decode_modes="ctc_greedy_search ctc_prefix_beam_search attention attention_rescoring"
decode_modes="ctc_prefix_beam_search"

  # Test model, please specify the model you want to test by --checkpoint
  if [ ${average_checkpoint} == true ]; then
    decode_checkpoint=$dir/avg_${average_num}.pt
    echo "do model average and final checkpoint is $decode_checkpoint"
    python wenet/bin/average_model.py \
      --dst_model $decode_checkpoint \
      --src_path $dir  \
      --num ${average_num} \
      --val_best
  fi
  # Please specify decoding_chunk_size for unified streaming and
  # non-streaming model. The default value is -1, which is full chunk
  # for non-streaming inference.
  decoding_chunk_size=-1
  ctc_weight=0.3
  reverse_weight=0.5
  for mode in ${decode_modes}; do
  {
    test_dir=$dir/test_${mode}
    mkdir -p $test_dir
    python wenet/bin/recognize.py --gpu $gpuid \
      --mode $mode \
      --config $dir/train.yaml \
      --data_type raw \
      --test_data $testdata/data.list \
      --checkpoint $decode_checkpoint \
      --beam_size 10 \
      --batch_size 1 \
      --penalty 0.0 \
      --dict $dict \
      --ctc_weight $ctc_weight \
      --reverse_weight $reverse_weight \
      --result_file $test_dir/text.a5.cuishoucut \
      ${decoding_chunk_size:+--decoding_chunk_size $decoding_chunk_size}
    python tools/compute-wer.py --char=1 --v=1 \
      $testdata/text $test_dir/text.a5.cuishoucut > $test_dir/wer.a5.cuishoucut
  } &
  done
  wait

