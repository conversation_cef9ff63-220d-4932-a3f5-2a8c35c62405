run_u2_efficonformer_v2_shop.sh: init method is file:///data/cjm/asr_tool/wenet-main/examples/360sk/s0/exp/u2++_efficonformer_shop/ddp_init
total gpus is: 2
using torch ddp
/home/<USER>/miniconda3/envs/wenet/lib/python3.8/site-packages/torch/_jit_internal.py:726: FutureWarning: ignore(True) has been deprecated. TorchScript will now drop the function call on compilation. Use torch.jit.unused now. {}
  warnings.warn(
2023-07-05 15:05:42,592 INFO training on multiple gpus, this gpu 3
/home/<USER>/miniconda3/envs/wenet/lib/python3.8/site-packages/torch/_jit_internal.py:726: FutureWarning: ignore(True) has been deprecated. TorchScript will now drop the function call on compilation. Use torch.jit.unused now. {}
  warnings.warn(
2023-07-05 15:05:42,693 INFO training on multiple gpus, this gpu 2
2023-07-05 15:05:49,595 INFO Added key: store_based_barrier_key:1 to store for rank: 1
2023-07-05 15:05:50,625 INFO Added key: store_based_barrier_key:1 to store for rank: 0
2023-07-05 15:05:50,626 INFO Rank 0: Completed store-based barrier for key:store_based_barrier_key:1 with 2 nodes.
2023-07-05 15:05:50,627 INFO Rank 1: Completed store-based barrier for key:store_based_barrier_key:1 with 2 nodes.
2023-07-05 15:05:50,672 INFO input_layer = conv2d2, subsampling_class = <class 'wenet.efficient_conformer.subsampling.Conv2dSubsampling2'>
2023-07-05 15:05:50,676 INFO input_layer = conv2d2, subsampling_class = <class 'wenet.efficient_conformer.subsampling.Conv2dSubsampling2'>
2023-07-05 15:05:50,737 INFO stride_layer_idx= [3, 7], stride = [2, 2], cnn_module_kernel = [15, 15, 15], group_layer_idx = [3, 7], grouped_size = 3
2023-07-05 15:05:50,744 INFO stride_layer_idx= [3, 7], stride = [2, 2], cnn_module_kernel = [15, 15, 15], group_layer_idx = [3, 7], grouped_size = 3
2023-07-05 15:05:51,097 INFO Checkpoint: loading from checkpoint exp/u2++_efficonformer_shop/19.pt for GPU
Failed to import k2 and icefall.         Notice that they are necessary for         hlg_onebest/hlg_rescore decoding and LF-MMI training
ASRModel(
  (encoder): EfficientConformerEncoder(
    (global_cmvn): GlobalCMVN()
    (embed): Conv2dSubsampling2(
      (conv): Sequential(
        (0): Conv2d(1, 256, kernel_size=(3, 3), stride=(2, 2))
        (1): ReLU()
      )
      (out): Sequential(
        (0): Linear(in_features=4864, out_features=256, bias=True)
      )
      (pos_enc): RelPositionalEncoding(
        (dropout): Dropout(p=0.1, inplace=False)
      )
    )
    (after_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    (encoders): ModuleList(
      (0): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), padding=(7,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (1): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), padding=(7,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (2): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), padding=(7,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (3): StrideConformerEncoderLayer(
        (self_attn): GroupedRelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(2,), padding=(7,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (pointwise_conv_layer): AvgPool1d(kernel_size=(2,), stride=(2,), padding=(0,))
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (concat_linear): Linear(in_features=512, out_features=256, bias=True)
      )
      (4): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), padding=(7,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (5): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), padding=(7,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (6): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), padding=(7,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (7): StrideConformerEncoderLayer(
        (self_attn): GroupedRelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(2,), padding=(7,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (pointwise_conv_layer): AvgPool1d(kernel_size=(2,), stride=(2,), padding=(0,))
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (concat_linear): Linear(in_features=512, out_features=256, bias=True)
      )
      (8): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), padding=(7,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (9): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), padding=(7,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (10): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), padding=(7,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (11): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), padding=(7,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
    )
  )
  (decoder): BiTransformerDecoder(
    (left_decoder): TransformerDecoder(
      (embed): Sequential(
        (0): Embedding(4802, 256)
        (1): PositionalEncoding(
          (dropout): Dropout(p=0.1, inplace=False)
        )
      )
      (after_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
      (output_layer): Linear(in_features=256, out_features=4802, bias=True)
      (decoders): ModuleList(
        (0): DecoderLayer(
          (self_attn): MultiHeadedAttention(
            (linear_q): Linear(in_features=256, out_features=256, bias=True)
            (linear_k): Linear(in_features=256, out_features=256, bias=True)
            (linear_v): Linear(in_features=256, out_features=256, bias=True)
            (linear_out): Linear(in_features=256, out_features=256, bias=True)
            (dropout): Dropout(p=0.1, inplace=False)
          )
          (src_attn): MultiHeadedAttention(
            (linear_q): Linear(in_features=256, out_features=256, bias=True)
            (linear_k): Linear(in_features=256, out_features=256, bias=True)
            (linear_v): Linear(in_features=256, out_features=256, bias=True)
            (linear_out): Linear(in_features=256, out_features=256, bias=True)
            (dropout): Dropout(p=0.1, inplace=False)
          )
          (feed_forward): PositionwiseFeedForward(
            (w_1): Linear(in_features=256, out_features=2048, bias=True)
            (activation): ReLU()
            (dropout): Dropout(p=0.1, inplace=False)
            (w_2): Linear(in_features=2048, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (dropout): Dropout(p=0.1, inplace=False)
        )
        (1): DecoderLayer(
          (self_attn): MultiHeadedAttention(
            (linear_q): Linear(in_features=256, out_features=256, bias=True)
            (linear_k): Linear(in_features=256, out_features=256, bias=True)
            (linear_v): Linear(in_features=256, out_features=256, bias=True)
            (linear_out): Linear(in_features=256, out_features=256, bias=True)
            (dropout): Dropout(p=0.1, inplace=False)
          )
          (src_attn): MultiHeadedAttention(
            (linear_q): Linear(in_features=256, out_features=256, bias=True)
            (linear_k): Linear(in_features=256, out_features=256, bias=True)
            (linear_v): Linear(in_features=256, out_features=256, bias=True)
            (linear_out): Linear(in_features=256, out_features=256, bias=True)
            (dropout): Dropout(p=0.1, inplace=False)
          )
          (feed_forward): PositionwiseFeedForward(
            (w_1): Linear(in_features=256, out_features=2048, bias=True)
            (activation): ReLU()
            (dropout): Dropout(p=0.1, inplace=False)
            (w_2): Linear(in_features=2048, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (dropout): Dropout(p=0.1, inplace=False)
        )
        (2): DecoderLayer(
          (self_attn): MultiHeadedAttention(
            (linear_q): Linear(in_features=256, out_features=256, bias=True)
            (linear_k): Linear(in_features=256, out_features=256, bias=True)
            (linear_v): Linear(in_features=256, out_features=256, bias=True)
            (linear_out): Linear(in_features=256, out_features=256, bias=True)
            (dropout): Dropout(p=0.1, inplace=False)
          )
          (src_attn): MultiHeadedAttention(
            (linear_q): Linear(in_features=256, out_features=256, bias=True)
            (linear_k): Linear(in_features=256, out_features=256, bias=True)
            (linear_v): Linear(in_features=256, out_features=256, bias=True)
            (linear_out): Linear(in_features=256, out_features=256, bias=True)
            (dropout): Dropout(p=0.1, inplace=False)
          )
          (feed_forward): PositionwiseFeedForward(
            (w_1): Linear(in_features=256, out_features=2048, bias=True)
            (activation): ReLU()
            (dropout): Dropout(p=0.1, inplace=False)
            (w_2): Linear(in_features=2048, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (dropout): Dropout(p=0.1, inplace=False)
        )
      )
    )
    (right_decoder): TransformerDecoder(
      (embed): Sequential(
        (0): Embedding(4802, 256)
        (1): PositionalEncoding(
          (dropout): Dropout(p=0.1, inplace=False)
        )
      )
      (after_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
      (output_layer): Linear(in_features=256, out_features=4802, bias=True)
      (decoders): ModuleList(
        (0): DecoderLayer(
          (self_attn): MultiHeadedAttention(
            (linear_q): Linear(in_features=256, out_features=256, bias=True)
            (linear_k): Linear(in_features=256, out_features=256, bias=True)
            (linear_v): Linear(in_features=256, out_features=256, bias=True)
            (linear_out): Linear(in_features=256, out_features=256, bias=True)
            (dropout): Dropout(p=0.1, inplace=False)
          )
          (src_attn): MultiHeadedAttention(
            (linear_q): Linear(in_features=256, out_features=256, bias=True)
            (linear_k): Linear(in_features=256, out_features=256, bias=True)
            (linear_v): Linear(in_features=256, out_features=256, bias=True)
            (linear_out): Linear(in_features=256, out_features=256, bias=True)
            (dropout): Dropout(p=0.1, inplace=False)
          )
          (feed_forward): PositionwiseFeedForward(
            (w_1): Linear(in_features=256, out_features=2048, bias=True)
            (activation): ReLU()
            (dropout): Dropout(p=0.1, inplace=False)
            (w_2): Linear(in_features=2048, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (dropout): Dropout(p=0.1, inplace=False)
        )
        (1): DecoderLayer(
          (self_attn): MultiHeadedAttention(
            (linear_q): Linear(in_features=256, out_features=256, bias=True)
            (linear_k): Linear(in_features=256, out_features=256, bias=True)
            (linear_v): Linear(in_features=256, out_features=256, bias=True)
            (linear_out): Linear(in_features=256, out_features=256, bias=True)
            (dropout): Dropout(p=0.1, inplace=False)
          )
          (src_attn): MultiHeadedAttention(
            (linear_q): Linear(in_features=256, out_features=256, bias=True)
            (linear_k): Linear(in_features=256, out_features=256, bias=True)
            (linear_v): Linear(in_features=256, out_features=256, bias=True)
            (linear_out): Linear(in_features=256, out_features=256, bias=True)
            (dropout): Dropout(p=0.1, inplace=False)
          )
          (feed_forward): PositionwiseFeedForward(
            (w_1): Linear(in_features=256, out_features=2048, bias=True)
            (activation): ReLU()
            (dropout): Dropout(p=0.1, inplace=False)
            (w_2): Linear(in_features=2048, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (dropout): Dropout(p=0.1, inplace=False)
        )
        (2): DecoderLayer(
          (self_attn): MultiHeadedAttention(
            (linear_q): Linear(in_features=256, out_features=256, bias=True)
            (linear_k): Linear(in_features=256, out_features=256, bias=True)
            (linear_v): Linear(in_features=256, out_features=256, bias=True)
            (linear_out): Linear(in_features=256, out_features=256, bias=True)
            (dropout): Dropout(p=0.1, inplace=False)
          )
          (src_attn): MultiHeadedAttention(
            (linear_q): Linear(in_features=256, out_features=256, bias=True)
            (linear_k): Linear(in_features=256, out_features=256, bias=True)
            (linear_v): Linear(in_features=256, out_features=256, bias=True)
            (linear_out): Linear(in_features=256, out_features=256, bias=True)
            (dropout): Dropout(p=0.1, inplace=False)
          )
          (feed_forward): PositionwiseFeedForward(
            (w_1): Linear(in_features=256, out_features=2048, bias=True)
            (activation): ReLU()
            (dropout): Dropout(p=0.1, inplace=False)
            (w_2): Linear(in_features=2048, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (dropout): Dropout(p=0.1, inplace=False)
        )
      )
    )
  )
  (ctc): CTC(
    (ctc_lo): Linear(in_features=256, out_features=4802, bias=True)
    (ctc_loss): CTCLoss()
  )
  (criterion_att): LabelSmoothingLoss(
    (criterion): KLDivLoss()
  )
)2023-07-05 15:05:52,512 INFO Checkpoint: loading from checkpoint exp/u2++_efficonformer_shop/19.pt for GPU
hpcamp18:102270:102270 [0] NCCL INFO Bootstrap : Using eth4:10.228.64.149<0>
hpcamp18:102270:102270 [0] NCCL INFO NET/Plugin : No plugin found (libnccl-net.so), using internal implementation
hpcamp18:102270:102270 [0] NCCL INFO cudaDriverVersion 11060
NCCL version 2.14.3+cuda11.7
2023-07-05 15:05:58,280 INFO Epoch 20 TRAIN info lr 4e-08
2023-07-05 15:05:58,281 INFO Epoch 20 TRAIN info lr 4e-08
2023-07-05 15:05:58,282 INFO using accumulate grad, new batch size is 1 times larger than before
2023-07-05 15:05:58,283 INFO using accumulate grad, new batch size is 1 times larger than before

the number of model params: 48,773,958
Failed to import k2 and icefall.         Notice that they are necessary for         hlg_onebest/hlg_rescore decoding and LF-MMI training
hpcamp18:102271:102271 [0] NCCL INFO cudaDriverVersion 11060
hpcamp18:102271:102271 [0] NCCL INFO Bootstrap : Using eth4:10.228.64.149<0>
hpcamp18:102271:102271 [0] NCCL INFO NET/Plugin : No plugin found (libnccl-net.so), using internal implementation
hpcamp18:102271:102659 [0] NCCL INFO Failed to open libibverbs.so[.1]
hpcamp18:102271:102659 [0] NCCL INFO NET/Socket : Using [0]eth4:10.228.64.149<0>
hpcamp18:102271:102659 [0] NCCL INFO Using network Socket
hpcamp18:102271:102659 [0] NCCL INFO Setting affinity for GPU 3 to ffff0000,00000000,ffff0000,00000000
hpcamp18:102271:102659 [0] NCCL INFO Trees [0] -1/-1/-1->1->0 [1] -1/-1/-1->1->0
hpcamp18:102271:102659 [0] NCCL INFO Channel 00 : 1[dc000] -> 0[ce000] via SHM/direct/direct
hpcamp18:102271:102659 [0] NCCL INFO Channel 01 : 1[dc000] -> 0[ce000] via SHM/direct/direct
hpcamp18:102271:102659 [0] NCCL INFO Connected all rings
hpcamp18:102271:102659 [0] NCCL INFO Connected all trees
hpcamp18:102271:102659 [0] NCCL INFO threadThresholds 8/8/64 | 16/8/64 | 512 | 512
hpcamp18:102271:102659 [0] NCCL INFO 2 coll channels, 2 p2p channels, 2 p2p channels per peer
hpcamp18:102271:102659 [0] NCCL INFO comm 0x4be3e810 rank 1 nranks 2 cudaDev 0 busId dc000 - Init COMPLETE
hpcamp18:102270:102658 [0] NCCL INFO Failed to open libibverbs.so[.1]
hpcamp18:102270:102658 [0] NCCL INFO NET/Socket : Using [0]eth4:10.228.64.149<0>
hpcamp18:102270:102658 [0] NCCL INFO Using network Socket
hpcamp18:102270:102658 [0] NCCL INFO Setting affinity for GPU 2 to ffff0000,00000000,ffff0000,00000000
hpcamp18:102270:102658 [0] NCCL INFO Channel 00/02 :    0   1
hpcamp18:102270:102658 [0] NCCL INFO Channel 01/02 :    0   1
hpcamp18:102270:102658 [0] NCCL INFO Trees [0] 1/-1/-1->0->-1 [1] 1/-1/-1->0->-1
hpcamp18:102270:102658 [0] NCCL INFO Channel 00 : 0[ce000] -> 1[dc000] via SHM/direct/direct
hpcamp18:102270:102658 [0] NCCL INFO Channel 01 : 0[ce000] -> 1[dc000] via SHM/direct/direct
hpcamp18:102270:102658 [0] NCCL INFO Connected all rings
hpcamp18:102270:102658 [0] NCCL INFO Connected all trees
hpcamp18:102270:102658 [0] NCCL INFO threadThresholds 8/8/64 | 16/8/64 | 512 | 512
hpcamp18:102270:102658 [0] NCCL INFO 2 coll channels, 2 p2p channels, 2 p2p channels per peer
hpcamp18:102270:102658 [0] NCCL INFO comm 0x4b4f4390 rank 0 nranks 2 cudaDev 0 busId ce000 - Init COMPLETE
2023-07-05 15:06:10,118 DEBUG TRAIN Batch 20/0 loss 5.645325 loss_att 4.541004 loss_ctc 8.222073 lr 0.00009767 rank 1
2023-07-05 15:06:10,243 DEBUG TRAIN Batch 20/0 loss 3.955604 loss_att 3.424378 loss_ctc 5.195130 lr 0.00009767 rank 0
2023-07-05 15:10:39,146 DEBUG TRAIN Batch 20/1000 loss 5.865711 loss_att 4.869469 loss_ctc 8.190275 lr 0.00009765 rank 1
2023-07-05 15:10:39,151 DEBUG TRAIN Batch 20/1000 loss 3.128820 loss_att 2.520287 loss_ctc 4.548729 lr 0.00009765 rank 0
2023-07-05 15:15:29,306 DEBUG TRAIN Batch 20/2000 loss 2.502831 loss_att 2.044272 loss_ctc 3.572802 lr 0.00009764 rank 1
2023-07-05 15:15:29,307 DEBUG TRAIN Batch 20/2000 loss 4.656350 loss_att 3.738101 loss_ctc 6.798931 lr 0.00009764 rank 0
2023-07-05 15:21:12,577 DEBUG TRAIN Batch 20/3000 loss 5.831777 loss_att 5.097339 loss_ctc 7.545465 lr 0.00009762 rank 0
2023-07-05 15:21:12,582 DEBUG TRAIN Batch 20/3000 loss 4.759830 loss_att 3.889839 loss_ctc 6.789811 lr 0.00009762 rank 1
2023-07-05 15:25:50,468 DEBUG TRAIN Batch 20/4000 loss 4.227833 loss_att 3.558690 loss_ctc 5.789166 lr 0.00009760 rank 1
2023-07-05 15:25:50,484 DEBUG TRAIN Batch 20/4000 loss 3.672804 loss_att 3.118524 loss_ctc 4.966125 lr 0.00009760 rank 0
2023-07-05 15:27:54,341 WARNING NaN or Inf found in input tensor.
2023-07-05 15:30:33,700 DEBUG TRAIN Batch 20/5000 loss 5.697970 loss_att 4.897603 loss_ctc 7.565493 lr 0.00009758 rank 0
2023-07-05 15:30:33,703 DEBUG TRAIN Batch 20/5000 loss 6.142449 loss_att 5.588789 loss_ctc 7.434326 lr 0.00009758 rank 1
2023-07-05 15:35:08,471 DEBUG TRAIN Batch 20/6000 loss 5.079016 loss_att 3.877324 loss_ctc 7.882964 lr 0.00009756 rank 0
2023-07-05 15:35:08,476 DEBUG TRAIN Batch 20/6000 loss 5.487819 loss_att 4.393362 loss_ctc 8.041553 lr 0.00009756 rank 1
2023-07-05 15:39:34,239 DEBUG TRAIN Batch 20/7000 loss 6.060499 loss_att 5.076362 loss_ctc 8.356820 lr 0.00009754 rank 0
2023-07-05 15:39:34,268 DEBUG TRAIN Batch 20/7000 loss 5.062716 loss_att 4.138620 loss_ctc 7.218939 lr 0.00009754 rank 1
2023-07-05 15:43:55,436 DEBUG TRAIN Batch 20/8000 loss 4.148797 loss_att 3.390747 loss_ctc 5.917578 lr 0.00009752 rank 0
2023-07-05 15:43:55,438 DEBUG TRAIN Batch 20/8000 loss 3.784319 loss_att 3.106479 loss_ctc 5.365948 lr 0.00009752 rank 1
2023-07-05 15:48:07,771 DEBUG TRAIN Batch 20/9000 loss 3.644676 loss_att 3.054041 loss_ctc 5.022826 lr 0.00009751 rank 1
2023-07-05 15:48:07,773 DEBUG TRAIN Batch 20/9000 loss 2.888965 loss_att 1.980996 loss_ctc 5.007560 lr 0.00009751 rank 0
2023-07-05 15:52:15,531 DEBUG TRAIN Batch 20/10000 loss 8.332907 loss_att 6.709846 loss_ctc 12.120049 lr 0.00009749 rank 0
2023-07-05 15:52:15,535 DEBUG TRAIN Batch 20/10000 loss 7.787104 loss_att 6.593422 loss_ctc 10.572361 lr 0.00009749 rank 1
2023-07-05 15:56:50,377 DEBUG TRAIN Batch 20/11000 loss 6.853759 loss_att 5.540899 loss_ctc 9.917097 lr 0.00009747 rank 0
2023-07-05 15:56:50,380 DEBUG TRAIN Batch 20/11000 loss 2.999052 loss_att 2.559346 loss_ctc 4.025031 lr 0.00009747 rank 1
wav: Premature EOF on .wav input file
2023-07-05 16:01:15,788 DEBUG TRAIN Batch 20/12000 loss 1.611691 loss_att 1.323179 loss_ctc 2.284887 lr 0.00009745 rank 0
2023-07-05 16:01:15,789 DEBUG TRAIN Batch 20/12000 loss 3.842714 loss_att 3.251543 loss_ctc 5.222116 lr 0.00009745 rank 1
2023-07-05 16:05:39,219 DEBUG TRAIN Batch 20/13000 loss 3.771047 loss_att 3.362426 loss_ctc 4.724496 lr 0.00009743 rank 1
2023-07-05 16:05:39,247 DEBUG TRAIN Batch 20/13000 loss 2.292475 loss_att 1.922787 loss_ctc 3.155079 lr 0.00009743 rank 0
2023-07-05 16:10:05,088 DEBUG TRAIN Batch 20/14000 loss 1.342829 loss_att 1.100945 loss_ctc 1.907225 lr 0.00009741 rank 0
2023-07-05 16:10:05,091 DEBUG TRAIN Batch 20/14000 loss 0.883330 loss_att 0.824848 loss_ctc 1.019788 lr 0.00009741 rank 1
2023-07-05 16:14:24,930 DEBUG TRAIN Batch 20/15000 loss 2.714621 loss_att 2.320596 loss_ctc 3.634011 lr 0.00009739 rank 0
2023-07-05 16:14:24,931 DEBUG TRAIN Batch 20/15000 loss 5.268433 loss_att 4.403851 loss_ctc 7.285791 lr 0.00009739 rank 1
2023-07-05 16:18:50,505 DEBUG TRAIN Batch 20/16000 loss 6.444119 loss_att 5.321416 loss_ctc 9.063761 lr 0.00009738 rank 0
2023-07-05 16:18:50,514 DEBUG TRAIN Batch 20/16000 loss 4.941696 loss_att 4.189840 loss_ctc 6.696025 lr 0.00009738 rank 1
2023-07-05 16:23:13,123 DEBUG TRAIN Batch 20/17000 loss 3.095498 loss_att 2.495514 loss_ctc 4.495461 lr 0.00009736 rank 0
2023-07-05 16:23:13,128 DEBUG TRAIN Batch 20/17000 loss 4.755636 loss_att 3.893197 loss_ctc 6.767996 lr 0.00009736 rank 1
2023-07-05 16:27:49,450 DEBUG TRAIN Batch 20/18000 loss 5.016469 loss_att 4.207389 loss_ctc 6.904322 lr 0.00009734 rank 0
2023-07-05 16:27:49,459 DEBUG TRAIN Batch 20/18000 loss 6.026508 loss_att 4.825545 loss_ctc 8.828754 lr 0.00009734 rank 1
2023-07-05 16:32:12,814 DEBUG TRAIN Batch 20/19000 loss 2.680174 loss_att 1.917891 loss_ctc 4.458835 lr 0.00009732 rank 0
2023-07-05 16:32:12,816 DEBUG TRAIN Batch 20/19000 loss 4.209972 loss_att 3.437134 loss_ctc 6.013262 lr 0.00009732 rank 1
2023-07-05 16:36:22,327 DEBUG TRAIN Batch 20/20000 loss 2.297472 loss_att 1.979031 loss_ctc 3.040500 lr 0.00009730 rank 1
2023-07-05 16:36:22,357 DEBUG TRAIN Batch 20/20000 loss 5.735069 loss_att 4.684287 loss_ctc 8.186893 lr 0.00009730 rank 0
2023-07-05 16:40:19,928 WARNING NaN or Inf found in input tensor.
2023-07-05 16:40:48,862 DEBUG TRAIN Batch 20/21000 loss 5.603076 loss_att 4.636807 loss_ctc 7.857703 lr 0.00009728 rank 1
2023-07-05 16:40:48,865 DEBUG TRAIN Batch 20/21000 loss 4.887890 loss_att 4.138100 loss_ctc 6.637402 lr 0.00009728 rank 0
2023-07-05 16:45:06,121 DEBUG TRAIN Batch 20/22000 loss 1.097051 loss_att 0.972413 loss_ctc 1.387871 lr 0.00009727 rank 0
2023-07-05 16:45:06,122 DEBUG TRAIN Batch 20/22000 loss 5.260302 loss_att 4.528080 loss_ctc 6.968818 lr 0.00009727 rank 1
2023-07-05 16:49:14,333 DEBUG TRAIN Batch 20/23000 loss 4.791955 loss_att 3.927720 loss_ctc 6.808506 lr 0.00009725 rank 0
2023-07-05 16:49:14,333 DEBUG TRAIN Batch 20/23000 loss 5.580347 loss_att 4.606633 loss_ctc 7.852348 lr 0.00009725 rank 1
2023-07-05 16:53:20,916 DEBUG TRAIN Batch 20/24000 loss 9.756542 loss_att 8.322318 loss_ctc 13.103065 lr 0.00009723 rank 0
2023-07-05 16:53:20,917 DEBUG TRAIN Batch 20/24000 loss 2.827037 loss_att 2.435889 loss_ctc 3.739716 lr 0.00009723 rank 1
2023-07-05 16:57:45,951 DEBUG TRAIN Batch 20/25000 loss 4.223412 loss_att 3.535151 loss_ctc 5.829352 lr 0.00009721 rank 0
2023-07-05 16:57:45,961 DEBUG TRAIN Batch 20/25000 loss 3.717261 loss_att 3.201998 loss_ctc 4.919542 lr 0.00009721 rank 1
2023-07-05 17:02:08,751 DEBUG TRAIN Batch 20/26000 loss 7.799802 loss_att 6.416804 loss_ctc 11.026796 lr 0.00009719 rank 0
2023-07-05 17:02:08,764 DEBUG TRAIN Batch 20/26000 loss 3.381905 loss_att 2.755438 loss_ctc 4.843661 lr 0.00009719 rank 1
2023-07-05 17:06:19,113 DEBUG TRAIN Batch 20/27000 loss 8.753588 loss_att 7.158102 loss_ctc 12.476389 lr 0.00009717 rank 0
2023-07-05 17:06:19,117 DEBUG TRAIN Batch 20/27000 loss 3.325527 loss_att 2.826965 loss_ctc 4.488838 lr 0.00009717 rank 1
2023-07-05 17:10:30,997 DEBUG TRAIN Batch 20/28000 loss 4.431171 loss_att 3.620331 loss_ctc 6.323133 lr 0.00009716 rank 1
2023-07-05 17:10:31,016 DEBUG TRAIN Batch 20/28000 loss 8.052713 loss_att 6.844950 loss_ctc 10.870829 lr 0.00009716 rank 0
2023-07-05 17:14:56,298 DEBUG TRAIN Batch 20/29000 loss 3.344179 loss_att 2.776856 loss_ctc 4.667934 lr 0.00009714 rank 0
2023-07-05 17:14:56,300 DEBUG TRAIN Batch 20/29000 loss 3.901201 loss_att 3.371254 loss_ctc 5.137744 lr 0.00009714 rank 1
2023-07-05 17:19:13,464 DEBUG TRAIN Batch 20/30000 loss 3.916119 loss_att 3.527178 loss_ctc 4.823647 lr 0.00009712 rank 0
2023-07-05 17:19:13,464 DEBUG TRAIN Batch 20/30000 loss 3.323805 loss_att 2.862988 loss_ctc 4.399043 lr 0.00009712 rank 1
2023-07-05 17:23:38,984 DEBUG TRAIN Batch 20/31000 loss 3.119042 loss_att 2.637497 loss_ctc 4.242647 lr 0.00009710 rank 0
2023-07-05 17:23:39,025 DEBUG TRAIN Batch 20/31000 loss 6.170720 loss_att 4.956546 loss_ctc 9.003792 lr 0.00009710 rank 1
2023-07-05 17:27:54,033 DEBUG TRAIN Batch 20/32000 loss 5.903862 loss_att 4.967763 loss_ctc 8.088093 lr 0.00009708 rank 0
2023-07-05 17:27:54,058 DEBUG TRAIN Batch 20/32000 loss 2.049040 loss_att 1.785385 loss_ctc 2.664236 lr 0.00009708 rank 1
2023-07-05 17:32:12,571 DEBUG TRAIN Batch 20/33000 loss 3.438215 loss_att 2.766577 loss_ctc 5.005370 lr 0.00009706 rank 1
2023-07-05 17:32:12,572 DEBUG TRAIN Batch 20/33000 loss 7.809881 loss_att 6.605594 loss_ctc 10.619884 lr 0.00009706 rank 0
2023-07-05 17:36:36,169 DEBUG TRAIN Batch 20/34000 loss 2.337158 loss_att 1.918347 loss_ctc 3.314382 lr 0.00009705 rank 0
2023-07-05 17:36:36,169 DEBUG TRAIN Batch 20/34000 loss 2.394845 loss_att 2.054199 loss_ctc 3.189685 lr 0.00009705 rank 1
2023-07-05 17:40:54,896 DEBUG TRAIN Batch 20/35000 loss 6.373013 loss_att 5.155325 loss_ctc 9.214283 lr 0.00009703 rank 0
2023-07-05 17:40:54,937 DEBUG TRAIN Batch 20/35000 loss 4.717478 loss_att 3.941380 loss_ctc 6.528373 lr 0.00009703 rank 1
2023-07-05 17:45:07,527 DEBUG TRAIN Batch 20/36000 loss 5.287955 loss_att 4.459928 loss_ctc 7.220020 lr 0.00009701 rank 0
2023-07-05 17:45:07,528 DEBUG TRAIN Batch 20/36000 loss 2.427852 loss_att 2.131511 loss_ctc 3.119316 lr 0.00009701 rank 1
2023-07-05 17:49:24,059 DEBUG TRAIN Batch 20/37000 loss 7.167831 loss_att 6.084798 loss_ctc 9.694908 lr 0.00009699 rank 1
2023-07-05 17:49:24,062 DEBUG TRAIN Batch 20/37000 loss 2.445422 loss_att 2.179904 loss_ctc 3.064965 lr 0.00009699 rank 0
2023-07-05 17:53:32,282 DEBUG TRAIN Batch 20/38000 loss 1.996605 loss_att 1.608140 loss_ctc 2.903025 lr 0.00009697 rank 0
2023-07-05 17:53:32,286 DEBUG TRAIN Batch 20/38000 loss 5.444968 loss_att 4.656080 loss_ctc 7.285706 lr 0.00009697 rank 1
2023-07-05 17:57:55,838 DEBUG TRAIN Batch 20/39000 loss 3.023971 loss_att 2.572760 loss_ctc 4.076795 lr 0.00009695 rank 0
2023-07-05 17:57:55,840 DEBUG TRAIN Batch 20/39000 loss 3.807144 loss_att 3.177614 loss_ctc 5.276046 lr 0.00009695 rank 1
2023-07-05 18:02:09,709 DEBUG TRAIN Batch 20/40000 loss 5.739105 loss_att 5.122638 loss_ctc 7.177529 lr 0.00009694 rank 0
2023-07-05 18:02:09,714 DEBUG TRAIN Batch 20/40000 loss 2.847161 loss_att 2.312687 loss_ctc 4.094265 lr 0.00009694 rank 1
2023-07-05 18:06:36,284 DEBUG TRAIN Batch 20/41000 loss 4.507001 loss_att 3.759781 loss_ctc 6.250514 lr 0.00009692 rank 0
2023-07-05 18:06:36,299 DEBUG TRAIN Batch 20/41000 loss 4.225088 loss_att 3.568358 loss_ctc 5.757459 lr 0.00009692 rank 1
2023-07-05 18:10:57,391 DEBUG TRAIN Batch 20/42000 loss 4.288301 loss_att 3.441225 loss_ctc 6.264811 lr 0.00009690 rank 0
2023-07-05 18:10:57,392 DEBUG TRAIN Batch 20/42000 loss 3.107985 loss_att 2.630164 loss_ctc 4.222901 lr 0.00009690 rank 1
2023-07-05 18:15:22,143 DEBUG TRAIN Batch 20/43000 loss 6.829790 loss_att 5.625583 loss_ctc 9.639605 lr 0.00009688 rank 0
2023-07-05 18:15:22,144 DEBUG TRAIN Batch 20/43000 loss 4.298588 loss_att 3.632629 loss_ctc 5.852491 lr 0.00009688 rank 1
2023-07-05 18:19:42,900 DEBUG TRAIN Batch 20/44000 loss 4.020335 loss_att 3.457014 loss_ctc 5.334750 lr 0.00009686 rank 1
2023-07-05 18:19:42,928 DEBUG TRAIN Batch 20/44000 loss 7.715680 loss_att 6.479746 loss_ctc 10.599526 lr 0.00009686 rank 0
2023-07-05 18:24:03,377 DEBUG TRAIN Batch 20/45000 loss 2.916157 loss_att 2.521097 loss_ctc 3.837964 lr 0.00009685 rank 0
2023-07-05 18:24:03,403 DEBUG TRAIN Batch 20/45000 loss 3.315677 loss_att 2.634079 loss_ctc 4.906074 lr 0.00009685 rank 1
2023-07-05 18:28:25,387 DEBUG TRAIN Batch 20/46000 loss 3.579920 loss_att 2.804649 loss_ctc 5.388885 lr 0.00009683 rank 0
2023-07-05 18:28:25,389 DEBUG TRAIN Batch 20/46000 loss 3.463609 loss_att 2.857686 loss_ctc 4.877429 lr 0.00009683 rank 1
2023-07-05 18:32:44,197 DEBUG TRAIN Batch 20/47000 loss 6.674985 loss_att 5.826035 loss_ctc 8.655868 lr 0.00009681 rank 0
2023-07-05 18:32:44,202 DEBUG TRAIN Batch 20/47000 loss 4.229194 loss_att 3.555882 loss_ctc 5.800258 lr 0.00009681 rank 1
2023-07-05 18:37:03,022 DEBUG TRAIN Batch 20/48000 loss 5.551678 loss_att 4.840916 loss_ctc 7.210122 lr 0.00009679 rank 1
2023-07-05 18:37:03,023 DEBUG TRAIN Batch 20/48000 loss 8.235600 loss_att 6.891141 loss_ctc 11.372671 lr 0.00009679 rank 0
2023-07-05 18:38:06,991 WARNING NaN or Inf found in input tensor.
2023-07-05 18:41:06,581 DEBUG TRAIN Batch 20/49000 loss 2.328925 loss_att 1.986168 loss_ctc 3.128692 lr 0.00009677 rank 1
2023-07-05 18:41:06,582 DEBUG TRAIN Batch 20/49000 loss 2.765858 loss_att 2.067985 loss_ctc 4.394228 lr 0.00009677 rank 0
2023-07-05 18:45:08,307 DEBUG TRAIN Batch 20/50000 loss 4.115929 loss_att 3.288491 loss_ctc 6.046618 lr 0.00009675 rank 0
2023-07-05 18:45:08,344 DEBUG TRAIN Batch 20/50000 loss 2.313563 loss_att 1.911326 loss_ctc 3.252116 lr 0.00009675 rank 1
2023-07-05 18:49:13,890 DEBUG TRAIN Batch 20/51000 loss 4.520426 loss_att 4.084209 loss_ctc 5.538265 lr 0.00009674 rank 0
2023-07-05 18:49:13,907 DEBUG TRAIN Batch 20/51000 loss 5.778317 loss_att 4.454044 loss_ctc 8.868289 lr 0.00009674 rank 1
2023-07-05 18:53:09,588 WARNING NaN or Inf found in input tensor.
2023-07-05 18:53:29,216 DEBUG TRAIN Batch 20/52000 loss 6.823175 loss_att 5.174582 loss_ctc 10.669893 lr 0.00009672 rank 0
2023-07-05 18:53:29,218 DEBUG TRAIN Batch 20/52000 loss 1.973496 loss_att 1.518499 loss_ctc 3.035155 lr 0.00009672 rank 1
2023-07-05 18:57:37,953 DEBUG TRAIN Batch 20/53000 loss 5.723331 loss_att 4.556749 loss_ctc 8.445356 lr 0.00009670 rank 1
2023-07-05 18:57:37,960 DEBUG TRAIN Batch 20/53000 loss 6.101516 loss_att 5.152624 loss_ctc 8.315598 lr 0.00009670 rank 0
2023-07-05 19:01:42,948 DEBUG TRAIN Batch 20/54000 loss 5.078043 loss_att 4.140185 loss_ctc 7.266376 lr 0.00009668 rank 1
2023-07-05 19:01:42,950 DEBUG TRAIN Batch 20/54000 loss 5.303192 loss_att 4.516857 loss_ctc 7.137973 lr 0.00009668 rank 0
2023-07-05 19:05:47,174 DEBUG TRAIN Batch 20/55000 loss 4.664437 loss_att 3.790363 loss_ctc 6.703943 lr 0.00009666 rank 1
2023-07-05 19:05:47,175 DEBUG TRAIN Batch 20/55000 loss 2.575469 loss_att 2.228647 loss_ctc 3.384721 lr 0.00009666 rank 0
2023-07-05 19:09:49,296 DEBUG TRAIN Batch 20/56000 loss 6.050950 loss_att 5.000595 loss_ctc 8.501779 lr 0.00009665 rank 1
2023-07-05 19:09:49,330 DEBUG TRAIN Batch 20/56000 loss 3.087038 loss_att 2.476369 loss_ctc 4.511930 lr 0.00009665 rank 0
2023-07-05 19:13:59,157 DEBUG TRAIN Batch 20/57000 loss 4.775244 loss_att 4.102077 loss_ctc 6.345966 lr 0.00009663 rank 1
2023-07-05 19:13:59,162 DEBUG TRAIN Batch 20/57000 loss 4.198120 loss_att 3.379592 loss_ctc 6.108019 lr 0.00009663 rank 0
2023-07-05 19:18:13,745 DEBUG TRAIN Batch 20/58000 loss 4.753926 loss_att 3.824348 loss_ctc 6.922942 lr 0.00009661 rank 1
2023-07-05 19:18:13,749 DEBUG TRAIN Batch 20/58000 loss 4.027450 loss_att 3.455546 loss_ctc 5.361893 lr 0.00009661 rank 0
2023-07-05 19:22:26,938 DEBUG TRAIN Batch 20/59000 loss 3.391511 loss_att 2.927405 loss_ctc 4.474425 lr 0.00009659 rank 1
2023-07-05 19:22:26,939 DEBUG TRAIN Batch 20/59000 loss 5.637837 loss_att 5.120517 loss_ctc 6.844918 lr 0.00009659 rank 0
2023-07-05 19:26:33,368 DEBUG TRAIN Batch 20/60000 loss 2.854548 loss_att 2.384085 loss_ctc 3.952296 lr 0.00009657 rank 0
2023-07-05 19:26:33,368 DEBUG TRAIN Batch 20/60000 loss 6.775567 loss_att 5.358407 loss_ctc 10.082272 lr 0.00009657 rank 1
2023-07-05 19:30:42,720 DEBUG TRAIN Batch 20/61000 loss 4.717247 loss_att 3.985352 loss_ctc 6.425000 lr 0.00009656 rank 1
2023-07-05 19:30:42,722 DEBUG TRAIN Batch 20/61000 loss 3.018448 loss_att 2.701978 loss_ctc 3.756880 lr 0.00009656 rank 0
2023-07-05 19:34:53,482 DEBUG TRAIN Batch 20/62000 loss 5.430199 loss_att 4.401385 loss_ctc 7.830763 lr 0.00009654 rank 1
2023-07-05 19:34:53,484 DEBUG TRAIN Batch 20/62000 loss 4.629786 loss_att 3.847669 loss_ctc 6.454726 lr 0.00009654 rank 0
2023-07-05 19:39:06,095 DEBUG TRAIN Batch 20/63000 loss 4.726357 loss_att 3.822330 loss_ctc 6.835752 lr 0.00009652 rank 1
2023-07-05 19:39:06,096 DEBUG TRAIN Batch 20/63000 loss 4.209324 loss_att 3.650220 loss_ctc 5.513898 lr 0.00009652 rank 0
2023-07-05 19:43:30,796 DEBUG TRAIN Batch 20/64000 loss 5.567083 loss_att 4.603469 loss_ctc 7.815516 lr 0.00009650 rank 0
2023-07-05 19:43:30,796 DEBUG TRAIN Batch 20/64000 loss 5.163691 loss_att 4.476556 loss_ctc 6.767005 lr 0.00009650 rank 1
2023-07-05 19:47:36,228 DEBUG TRAIN Batch 20/65000 loss 2.904001 loss_att 2.700797 loss_ctc 3.378143 lr 0.00009648 rank 1
2023-07-05 19:47:36,231 DEBUG TRAIN Batch 20/65000 loss 4.927724 loss_att 3.992301 loss_ctc 7.110378 lr 0.00009648 rank 0
2023-07-05 19:51:39,719 DEBUG TRAIN Batch 20/66000 loss 3.090804 loss_att 2.662310 loss_ctc 4.090622 lr 0.00009647 rank 1
2023-07-05 19:51:39,719 DEBUG TRAIN Batch 20/66000 loss 6.084853 loss_att 4.970781 loss_ctc 8.684355 lr 0.00009647 rank 0
2023-07-05 19:55:56,197 DEBUG TRAIN Batch 20/67000 loss 2.054671 loss_att 1.665006 loss_ctc 2.963890 lr 0.00009645 rank 1
2023-07-05 19:55:56,198 DEBUG TRAIN Batch 20/67000 loss 2.293194 loss_att 1.937295 loss_ctc 3.123626 lr 0.00009645 rank 0
2023-07-05 20:00:08,320 DEBUG TRAIN Batch 20/68000 loss 5.923771 loss_att 5.191539 loss_ctc 7.632312 lr 0.00009643 rank 1
2023-07-05 20:00:08,322 DEBUG TRAIN Batch 20/68000 loss 4.043602 loss_att 3.581170 loss_ctc 5.122609 lr 0.00009643 rank 0
2023-07-05 20:04:17,384 DEBUG TRAIN Batch 20/69000 loss 6.271925 loss_att 4.898034 loss_ctc 9.477670 lr 0.00009641 rank 1
2023-07-05 20:04:17,387 DEBUG TRAIN Batch 20/69000 loss 4.370083 loss_att 3.609492 loss_ctc 6.144796 lr 0.00009641 rank 0
2023-07-05 20:08:33,695 DEBUG TRAIN Batch 20/70000 loss 7.041276 loss_att 5.958325 loss_ctc 9.568160 lr 0.00009639 rank 0
2023-07-05 20:08:33,726 DEBUG TRAIN Batch 20/70000 loss 6.679366 loss_att 5.830366 loss_ctc 8.660367 lr 0.00009639 rank 1
2023-07-05 20:12:39,622 DEBUG TRAIN Batch 20/71000 loss 3.436268 loss_att 2.910609 loss_ctc 4.662804 lr 0.00009638 rank 0
2023-07-05 20:12:39,627 DEBUG TRAIN Batch 20/71000 loss 5.998285 loss_att 4.561866 loss_ctc 9.349930 lr 0.00009638 rank 1
2023-07-05 20:16:53,900 DEBUG TRAIN Batch 20/72000 loss 8.193413 loss_att 6.645407 loss_ctc 11.805428 lr 0.00009636 rank 1
2023-07-05 20:16:53,904 DEBUG TRAIN Batch 20/72000 loss 8.171243 loss_att 7.087750 loss_ctc 10.699390 lr 0.00009636 rank 0
2023-07-05 20:21:10,105 DEBUG TRAIN Batch 20/73000 loss 3.362390 loss_att 2.931175 loss_ctc 4.368558 lr 0.00009634 rank 1
2023-07-05 20:21:10,109 DEBUG TRAIN Batch 20/73000 loss 3.424195 loss_att 3.059238 loss_ctc 4.275760 lr 0.00009634 rank 0
2023-07-05 20:25:17,669 DEBUG TRAIN Batch 20/74000 loss 4.350990 loss_att 3.593787 loss_ctc 6.117796 lr 0.00009632 rank 1
2023-07-05 20:25:17,670 DEBUG TRAIN Batch 20/74000 loss 8.301088 loss_att 7.234080 loss_ctc 10.790775 lr 0.00009632 rank 0
2023-07-05 20:29:25,335 DEBUG TRAIN Batch 20/75000 loss 4.070745 loss_att 3.493000 loss_ctc 5.418815 lr 0.00009630 rank 1
2023-07-05 20:29:25,338 DEBUG TRAIN Batch 20/75000 loss 5.103374 loss_att 4.553189 loss_ctc 6.387142 lr 0.00009630 rank 0
2023-07-05 20:33:47,635 DEBUG TRAIN Batch 20/76000 loss 3.574832 loss_att 3.040730 loss_ctc 4.821070 lr 0.00009629 rank 0
2023-07-05 20:33:47,635 DEBUG TRAIN Batch 20/76000 loss 1.735651 loss_att 1.586758 loss_ctc 2.083068 lr 0.00009629 rank 1
2023-07-05 20:37:57,309 DEBUG TRAIN Batch 20/77000 loss 1.434731 loss_att 1.106884 loss_ctc 2.199710 lr 0.00009627 rank 1
2023-07-05 20:37:57,314 DEBUG TRAIN Batch 20/77000 loss 2.360531 loss_att 2.020907 loss_ctc 3.152987 lr 0.00009627 rank 0
2023-07-05 20:42:02,330 DEBUG TRAIN Batch 20/78000 loss 2.874613 loss_att 2.342872 loss_ctc 4.115341 lr 0.00009625 rank 0
2023-07-05 20:42:02,355 DEBUG TRAIN Batch 20/78000 loss 9.545245 loss_att 7.652681 loss_ctc 13.961226 lr 0.00009625 rank 1
2023-07-05 20:46:09,188 DEBUG TRAIN Batch 20/79000 loss 3.805902 loss_att 3.250367 loss_ctc 5.102151 lr 0.00009623 rank 0
2023-07-05 20:46:09,212 DEBUG TRAIN Batch 20/79000 loss 6.171432 loss_att 4.785482 loss_ctc 9.405314 lr 0.00009623 rank 1
2023-07-05 20:50:27,247 DEBUG TRAIN Batch 20/80000 loss 4.215411 loss_att 3.421896 loss_ctc 6.066947 lr 0.00009622 rank 1
2023-07-05 20:50:27,253 DEBUG TRAIN Batch 20/80000 loss 3.953305 loss_att 3.312778 loss_ctc 5.447869 lr 0.00009622 rank 0
2023-07-05 20:54:40,921 DEBUG TRAIN Batch 20/81000 loss 3.577652 loss_att 3.049830 loss_ctc 4.809237 lr 0.00009620 rank 1
2023-07-05 20:54:40,921 DEBUG TRAIN Batch 20/81000 loss 6.455997 loss_att 5.558134 loss_ctc 8.551014 lr 0.00009620 rank 0
2023-07-05 20:58:53,224 DEBUG TRAIN Batch 20/82000 loss 4.503806 loss_att 3.778396 loss_ctc 6.196429 lr 0.00009618 rank 1
2023-07-05 20:58:53,229 DEBUG TRAIN Batch 20/82000 loss 3.088801 loss_att 2.508827 loss_ctc 4.442076 lr 0.00009618 rank 0
2023-07-05 21:03:06,457 DEBUG TRAIN Batch 20/83000 loss 3.542157 loss_att 2.971509 loss_ctc 4.873668 lr 0.00009616 rank 1
2023-07-05 21:03:06,462 DEBUG TRAIN Batch 20/83000 loss 5.592963 loss_att 4.730296 loss_ctc 7.605854 lr 0.00009616 rank 0
2023-07-05 21:07:23,252 DEBUG TRAIN Batch 20/84000 loss 4.828009 loss_att 3.811446 loss_ctc 7.199989 lr 0.00009614 rank 1
2023-07-05 21:07:23,255 DEBUG TRAIN Batch 20/84000 loss 3.133811 loss_att 2.724965 loss_ctc 4.087783 lr 0.00009614 rank 0
2023-07-05 21:11:34,177 DEBUG TRAIN Batch 20/85000 loss 4.265123 loss_att 3.528062 loss_ctc 5.984933 lr 0.00009613 rank 1
2023-07-05 21:11:34,178 DEBUG TRAIN Batch 20/85000 loss 3.729892 loss_att 3.019391 loss_ctc 5.387726 lr 0.00009613 rank 0
2023-07-05 21:11:41,972 WARNING NaN or Inf found in input tensor.
2023-07-05 21:15:43,735 DEBUG TRAIN Batch 20/86000 loss 2.032065 loss_att 1.649021 loss_ctc 2.925835 lr 0.00009611 rank 1
2023-07-05 21:15:43,736 DEBUG TRAIN Batch 20/86000 loss 6.523085 loss_att 5.415362 loss_ctc 9.107771 lr 0.00009611 rank 0
2023-07-05 21:19:54,252 DEBUG TRAIN Batch 20/87000 loss 3.164321 loss_att 2.728479 loss_ctc 4.181285 lr 0.00009609 rank 1
2023-07-05 21:19:54,254 DEBUG TRAIN Batch 20/87000 loss 4.857979 loss_att 4.190505 loss_ctc 6.415417 lr 0.00009609 rank 0
2023-07-05 21:24:09,866 DEBUG TRAIN Batch 20/88000 loss 4.964094 loss_att 3.955806 loss_ctc 7.316764 lr 0.00009607 rank 1
2023-07-05 21:24:09,866 DEBUG TRAIN Batch 20/88000 loss 5.221983 loss_att 4.203756 loss_ctc 7.597844 lr 0.00009607 rank 0
2023-07-05 21:28:18,834 DEBUG TRAIN Batch 20/89000 loss 6.227010 loss_att 5.054382 loss_ctc 8.963140 lr 0.00009606 rank 1
2023-07-05 21:28:18,834 DEBUG TRAIN Batch 20/89000 loss 2.469268 loss_att 2.115957 loss_ctc 3.293661 lr 0.00009606 rank 0
2023-07-05 21:32:22,170 DEBUG TRAIN Batch 20/90000 loss 2.935536 loss_att 2.515892 loss_ctc 3.914706 lr 0.00009604 rank 0
2023-07-05 21:32:22,197 DEBUG TRAIN Batch 20/90000 loss 2.396139 loss_att 2.062005 loss_ctc 3.175786 lr 0.00009604 rank 1
2023-07-05 21:36:28,726 DEBUG TRAIN Batch 20/91000 loss 5.758247 loss_att 4.923310 loss_ctc 7.706434 lr 0.00009602 rank 1
2023-07-05 21:36:28,728 DEBUG TRAIN Batch 20/91000 loss 4.536594 loss_att 3.779471 loss_ctc 6.303215 lr 0.00009602 rank 0
2023-07-05 21:40:39,471 DEBUG TRAIN Batch 20/92000 loss 7.571475 loss_att 6.306664 loss_ctc 10.522700 lr 0.00009600 rank 1
2023-07-05 21:40:39,471 DEBUG TRAIN Batch 20/92000 loss 8.843614 loss_att 7.296289 loss_ctc 12.454037 lr 0.00009600 rank 0
2023-07-05 21:44:52,477 DEBUG TRAIN Batch 20/93000 loss 2.463191 loss_att 2.100994 loss_ctc 3.308317 lr 0.00009598 rank 1
2023-07-05 21:44:52,479 DEBUG TRAIN Batch 20/93000 loss 3.552559 loss_att 3.105191 loss_ctc 4.596418 lr 0.00009598 rank 0
2023-07-05 21:49:21,855 DEBUG TRAIN Batch 20/94000 loss 4.471155 loss_att 3.696697 loss_ctc 6.278222 lr 0.00009597 rank 1
2023-07-05 21:49:21,856 DEBUG TRAIN Batch 20/94000 loss 5.458466 loss_att 4.511129 loss_ctc 7.668918 lr 0.00009597 rank 0
2023-07-05 21:53:28,827 DEBUG TRAIN Batch 20/95000 loss 1.310245 loss_att 1.134804 loss_ctc 1.719607 lr 0.00009595 rank 1
2023-07-05 21:53:28,831 DEBUG TRAIN Batch 20/95000 loss 5.969558 loss_att 4.946869 loss_ctc 8.355829 lr 0.00009595 rank 0
2023-07-05 21:57:33,174 DEBUG TRAIN Batch 20/96000 loss 9.385018 loss_att 7.880915 loss_ctc 12.894592 lr 0.00009593 rank 1
2023-07-05 21:57:33,177 DEBUG TRAIN Batch 20/96000 loss 5.808311 loss_att 5.017449 loss_ctc 7.653658 lr 0.00009593 rank 0
2023-07-05 22:01:49,352 DEBUG TRAIN Batch 20/97000 loss 3.504862 loss_att 3.071812 loss_ctc 4.515312 lr 0.00009591 rank 0
2023-07-05 22:01:49,377 DEBUG TRAIN Batch 20/97000 loss 5.005920 loss_att 3.887208 loss_ctc 7.616249 lr 0.00009591 rank 1
2023-07-05 22:06:13,572 DEBUG TRAIN Batch 20/98000 loss 4.247021 loss_att 3.666304 loss_ctc 5.602027 lr 0.00009590 rank 0
2023-07-05 22:06:13,601 DEBUG TRAIN Batch 20/98000 loss 5.667197 loss_att 4.871845 loss_ctc 7.523021 lr 0.00009590 rank 1
2023-07-05 22:10:04,647 WARNING NaN or Inf found in input tensor.
2023-07-05 22:10:26,894 DEBUG TRAIN Batch 20/99000 loss 4.006147 loss_att 3.185060 loss_ctc 5.922018 lr 0.00009588 rank 1
2023-07-05 22:10:26,896 DEBUG TRAIN Batch 20/99000 loss 4.842860 loss_att 3.935078 loss_ctc 6.961018 lr 0.00009588 rank 0
2023-07-05 22:14:40,575 DEBUG TRAIN Batch 20/100000 loss 3.294824 loss_att 2.766113 loss_ctc 4.528481 lr 0.00009586 rank 0
2023-07-05 22:14:40,575 DEBUG TRAIN Batch 20/100000 loss 4.246162 loss_att 3.626703 loss_ctc 5.691567 lr 0.00009586 rank 1
2023-07-05 22:18:45,920 DEBUG TRAIN Batch 20/101000 loss 5.200428 loss_att 4.524079 loss_ctc 6.778574 lr 0.00009584 rank 1
2023-07-05 22:18:45,925 DEBUG TRAIN Batch 20/101000 loss 5.433911 loss_att 4.428467 loss_ctc 7.779949 lr 0.00009584 rank 0
2023-07-05 22:22:54,061 DEBUG TRAIN Batch 20/102000 loss 2.356366 loss_att 1.931701 loss_ctc 3.347252 lr 0.00009583 rank 1
2023-07-05 22:22:54,089 DEBUG TRAIN Batch 20/102000 loss 5.926778 loss_att 4.833707 loss_ctc 8.477276 lr 0.00009583 rank 0
2023-07-05 22:27:05,273 DEBUG TRAIN Batch 20/103000 loss 3.391293 loss_att 2.927718 loss_ctc 4.472968 lr 0.00009581 rank 1
2023-07-05 22:27:05,274 DEBUG TRAIN Batch 20/103000 loss 1.603657 loss_att 1.409835 loss_ctc 2.055907 lr 0.00009581 rank 0
2023-07-05 22:31:09,390 DEBUG TRAIN Batch 20/104000 loss 2.167632 loss_att 1.838211 loss_ctc 2.936280 lr 0.00009579 rank 0
2023-07-05 22:31:09,435 DEBUG TRAIN Batch 20/104000 loss 2.766645 loss_att 2.361418 loss_ctc 3.712175 lr 0.00009579 rank 1
2023-07-05 22:35:23,773 DEBUG TRAIN Batch 20/105000 loss 1.624377 loss_att 1.345017 loss_ctc 2.276216 lr 0.00009577 rank 1
2023-07-05 22:35:23,773 DEBUG TRAIN Batch 20/105000 loss 3.645092 loss_att 3.203303 loss_ctc 4.675932 lr 0.00009577 rank 0
2023-07-05 22:39:29,390 DEBUG TRAIN Batch 20/106000 loss 2.822105 loss_att 2.493650 loss_ctc 3.588500 lr 0.00009576 rank 0
2023-07-05 22:39:29,391 DEBUG TRAIN Batch 20/106000 loss 5.336168 loss_att 4.324958 loss_ctc 7.695658 lr 0.00009576 rank 1
2023-07-05 22:43:46,757 DEBUG TRAIN Batch 20/107000 loss 4.938233 loss_att 4.045475 loss_ctc 7.021337 lr 0.00009574 rank 1
2023-07-05 22:43:46,760 DEBUG TRAIN Batch 20/107000 loss 5.217941 loss_att 4.171978 loss_ctc 7.658523 lr 0.00009574 rank 0
2023-07-05 22:47:58,772 DEBUG TRAIN Batch 20/108000 loss 5.734470 loss_att 4.882485 loss_ctc 7.722436 lr 0.00009572 rank 1
2023-07-05 22:47:58,776 DEBUG TRAIN Batch 20/108000 loss 3.748686 loss_att 2.913168 loss_ctc 5.698228 lr 0.00009572 rank 0
2023-07-05 22:52:20,288 DEBUG TRAIN Batch 20/109000 loss 2.215374 loss_att 1.764811 loss_ctc 3.266686 lr 0.00009570 rank 1
2023-07-05 22:52:20,291 DEBUG TRAIN Batch 20/109000 loss 5.540040 loss_att 4.627097 loss_ctc 7.670242 lr 0.00009570 rank 0
2023-07-05 22:56:29,591 DEBUG TRAIN Batch 20/110000 loss 2.302186 loss_att 1.967892 loss_ctc 3.082204 lr 0.00009569 rank 0
2023-07-05 22:56:29,594 DEBUG TRAIN Batch 20/110000 loss 2.187974 loss_att 1.803979 loss_ctc 3.083962 lr 0.00009569 rank 1
2023-07-05 23:00:38,993 DEBUG TRAIN Batch 20/111000 loss 7.496540 loss_att 6.272546 loss_ctc 10.352528 lr 0.00009567 rank 1
2023-07-05 23:00:38,997 DEBUG TRAIN Batch 20/111000 loss 4.883811 loss_att 3.989082 loss_ctc 6.971510 lr 0.00009567 rank 0
2023-07-05 23:04:56,699 DEBUG TRAIN Batch 20/112000 loss 4.780187 loss_att 3.912560 loss_ctc 6.804648 lr 0.00009565 rank 0
2023-07-05 23:04:56,699 DEBUG TRAIN Batch 20/112000 loss 4.011379 loss_att 3.541712 loss_ctc 5.107268 lr 0.00009565 rank 1
2023-07-05 23:06:06,195 WARNING NaN or Inf found in input tensor.
2023-07-05 23:09:11,432 DEBUG TRAIN Batch 20/113000 loss 6.379823 loss_att 5.080705 loss_ctc 9.411099 lr 0.00009563 rank 1
2023-07-05 23:09:11,434 DEBUG TRAIN Batch 20/113000 loss 4.680313 loss_att 3.731852 loss_ctc 6.893388 lr 0.00009563 rank 0
2023-07-05 23:13:27,703 DEBUG TRAIN Batch 20/114000 loss 4.163360 loss_att 3.429760 loss_ctc 5.875094 lr 0.00009562 rank 0
2023-07-05 23:13:27,728 DEBUG TRAIN Batch 20/114000 loss 3.961630 loss_att 3.305855 loss_ctc 5.491772 lr 0.00009562 rank 1
2023-07-05 23:17:31,201 DEBUG TRAIN Batch 20/115000 loss 3.612974 loss_att 3.034851 loss_ctc 4.961930 lr 0.00009560 rank 1
2023-07-05 23:17:31,207 DEBUG TRAIN Batch 20/115000 loss 4.593189 loss_att 3.908912 loss_ctc 6.189834 lr 0.00009560 rank 0
2023-07-05 23:21:44,881 DEBUG TRAIN Batch 20/116000 loss 3.716842 loss_att 3.393157 loss_ctc 4.472107 lr 0.00009558 rank 1
2023-07-05 23:21:44,884 DEBUG TRAIN Batch 20/116000 loss 3.722215 loss_att 3.170500 loss_ctc 5.009551 lr 0.00009558 rank 0
2023-07-05 23:25:57,072 DEBUG TRAIN Batch 20/117000 loss 4.882490 loss_att 4.189435 loss_ctc 6.499617 lr 0.00009556 rank 1
2023-07-05 23:25:57,091 DEBUG TRAIN Batch 20/117000 loss 2.297513 loss_att 1.963192 loss_ctc 3.077596 lr 0.00009556 rank 0
2023-07-05 23:30:12,217 DEBUG TRAIN Batch 20/118000 loss 4.455747 loss_att 3.914823 loss_ctc 5.717903 lr 0.00009555 rank 1
2023-07-05 23:30:12,221 DEBUG TRAIN Batch 20/118000 loss 2.788029 loss_att 2.413908 loss_ctc 3.660979 lr 0.00009555 rank 0
2023-07-05 23:34:25,192 DEBUG TRAIN Batch 20/119000 loss 3.887167 loss_att 3.365332 loss_ctc 5.104779 lr 0.00009553 rank 1
2023-07-05 23:34:25,221 DEBUG TRAIN Batch 20/119000 loss 2.482201 loss_att 2.109288 loss_ctc 3.352330 lr 0.00009553 rank 0
2023-07-05 23:38:44,818 DEBUG TRAIN Batch 20/120000 loss 7.449604 loss_att 5.796461 loss_ctc 11.306936 lr 0.00009551 rank 1
2023-07-05 23:38:44,819 DEBUG TRAIN Batch 20/120000 loss 6.138990 loss_att 4.765812 loss_ctc 9.343073 lr 0.00009551 rank 0
2023-07-05 23:43:05,510 DEBUG TRAIN Batch 20/121000 loss 2.953168 loss_att 2.546736 loss_ctc 3.901510 lr 0.00009549 rank 1
2023-07-05 23:43:05,512 DEBUG TRAIN Batch 20/121000 loss 7.415228 loss_att 6.059652 loss_ctc 10.578239 lr 0.00009549 rank 0
2023-07-05 23:47:10,545 DEBUG TRAIN Batch 20/122000 loss 8.532793 loss_att 7.211028 loss_ctc 11.616911 lr 0.00009548 rank 1
2023-07-05 23:47:10,547 DEBUG TRAIN Batch 20/122000 loss 1.853480 loss_att 1.595505 loss_ctc 2.455420 lr 0.00009548 rank 0
2023-07-05 23:51:21,223 DEBUG TRAIN Batch 20/123000 loss 3.078734 loss_att 2.695913 loss_ctc 3.971985 lr 0.00009546 rank 1
2023-07-05 23:51:21,225 DEBUG TRAIN Batch 20/123000 loss 2.768779 loss_att 2.318546 loss_ctc 3.819324 lr 0.00009546 rank 0
2023-07-05 23:55:24,244 DEBUG TRAIN Batch 20/124000 loss 4.486851 loss_att 3.941061 loss_ctc 5.760361 lr 0.00009544 rank 0
2023-07-05 23:55:24,269 DEBUG TRAIN Batch 20/124000 loss 3.071266 loss_att 2.530471 loss_ctc 4.333120 lr 0.00009544 rank 1
2023-07-05 23:59:22,648 DEBUG TRAIN Batch 20/125000 loss 5.988122 loss_att 4.922508 loss_ctc 8.474552 lr 0.00009542 rank 1
2023-07-05 23:59:22,677 DEBUG TRAIN Batch 20/125000 loss 2.256975 loss_att 1.910028 loss_ctc 3.066519 lr 0.00009542 rank 0
2023-07-06 00:03:33,598 DEBUG TRAIN Batch 20/126000 loss 6.702274 loss_att 5.513552 loss_ctc 9.475961 lr 0.00009541 rank 1
2023-07-06 00:03:33,605 DEBUG TRAIN Batch 20/126000 loss 2.675100 loss_att 2.192880 loss_ctc 3.800278 lr 0.00009541 rank 0
2023-07-06 00:07:49,777 DEBUG TRAIN Batch 20/127000 loss 6.003340 loss_att 4.964086 loss_ctc 8.428267 lr 0.00009539 rank 1
2023-07-06 00:07:49,779 DEBUG TRAIN Batch 20/127000 loss 4.550626 loss_att 3.837125 loss_ctc 6.215462 lr 0.00009539 rank 0
2023-07-06 00:11:50,884 DEBUG TRAIN Batch 20/128000 loss 3.021556 loss_att 2.453784 loss_ctc 4.346355 lr 0.00009537 rank 1
2023-07-06 00:11:50,885 DEBUG TRAIN Batch 20/128000 loss 3.410386 loss_att 2.736925 loss_ctc 4.981793 lr 0.00009537 rank 0
2023-07-06 00:15:53,812 DEBUG TRAIN Batch 20/129000 loss 3.343701 loss_att 2.875580 loss_ctc 4.435982 lr 0.00009535 rank 1
2023-07-06 00:15:53,817 DEBUG TRAIN Batch 20/129000 loss 2.472959 loss_att 2.139356 loss_ctc 3.251366 lr 0.00009535 rank 0
2023-07-06 00:19:57,947 DEBUG TRAIN Batch 20/130000 loss 4.067715 loss_att 3.258567 loss_ctc 5.955726 lr 0.00009534 rank 1
2023-07-06 00:19:57,948 DEBUG TRAIN Batch 20/130000 loss 2.767653 loss_att 2.258638 loss_ctc 3.955354 lr 0.00009534 rank 0
2023-07-06 00:24:05,176 DEBUG TRAIN Batch 20/131000 loss 7.001184 loss_att 6.120391 loss_ctc 9.056366 lr 0.00009532 rank 1
2023-07-06 00:24:24,857 DEBUG CV Batch 20/0 loss 3.155194 loss_att 2.771641 loss_ctc 4.050149 history loss 3.059582 rank 0
2023-07-06 00:24:24,869 DEBUG CV Batch 20/0 loss 3.043295 loss_att 2.644352 loss_ctc 3.974164 history loss 2.951074 rank 1
2023-07-06 00:25:36,853 DEBUG CV Batch 20/1000 loss 1.687516 loss_att 1.622920 loss_ctc 1.838238 history loss 4.847705 rank 0
2023-07-06 00:25:36,861 DEBUG CV Batch 20/1000 loss 1.693019 loss_att 1.627674 loss_ctc 1.845490 history loss 4.851214 rank 1
2023-07-06 00:26:41,408 DEBUG CV Batch 20/2000 loss 2.419005 loss_att 2.273934 loss_ctc 2.757504 history loss 5.291463 rank 1
2023-07-06 00:26:41,416 DEBUG CV Batch 20/2000 loss 2.462751 loss_att 2.293675 loss_ctc 2.857263 history loss 5.289153 rank 0
2023-07-06 00:27:13,370 INFO Epoch 20 CV info cv_loss 5.43081788598909
2023-07-06 00:27:13,401 INFO Checkpoint: save to checkpoint exp/u2++_efficonformer_shop/20.pt
2023-07-06 00:27:13,652 INFO Epoch 21 TRAIN info lr 9.532038728316757e-05
2023-07-06 00:27:13,654 INFO using accumulate grad, new batch size is 1 times larger than before
2023-07-06 00:27:14,422 INFO Epoch 20 CV info cv_loss 5.436822322599842
2023-07-06 00:27:14,422 INFO Epoch 21 TRAIN info lr 9.531856856979664e-05
2023-07-06 00:27:14,430 INFO using accumulate grad, new batch size is 1 times larger than before
2023-07-06 00:27:22,869 DEBUG TRAIN Batch 21/0 loss 3.435708 loss_att 2.992661 loss_ctc 4.469482 lr 0.00009532 rank 0
2023-07-06 00:27:22,881 DEBUG TRAIN Batch 21/0 loss 8.806554 loss_att 7.213961 loss_ctc 12.522602 lr 0.00009532 rank 1
2023-07-06 00:31:44,461 DEBUG TRAIN Batch 21/1000 loss 6.175862 loss_att 5.154396 loss_ctc 8.559282 lr 0.00009530 rank 0
2023-07-06 00:31:44,466 DEBUG TRAIN Batch 21/1000 loss 5.598754 loss_att 4.722455 loss_ctc 7.643451 lr 0.00009530 rank 1
2023-07-06 00:36:00,957 DEBUG TRAIN Batch 21/2000 loss 7.248446 loss_att 5.862906 loss_ctc 10.481373 lr 0.00009529 rank 0
2023-07-06 00:36:00,957 DEBUG TRAIN Batch 21/2000 loss 2.548345 loss_att 2.108674 loss_ctc 3.574242 lr 0.00009528 rank 1
2023-07-06 00:38:15,681 WARNING NaN or Inf found in input tensor.
2023-07-06 00:40:01,107 DEBUG TRAIN Batch 21/3000 loss 2.356029 loss_att 2.106295 loss_ctc 2.938742 lr 0.00009527 rank 1
2023-07-06 00:40:01,111 DEBUG TRAIN Batch 21/3000 loss 3.152452 loss_att 2.513824 loss_ctc 4.642583 lr 0.00009527 rank 0
2023-07-06 00:44:26,691 DEBUG TRAIN Batch 21/4000 loss 4.533621 loss_att 3.670138 loss_ctc 6.548414 lr 0.00009525 rank 1
2023-07-06 00:44:26,695 DEBUG TRAIN Batch 21/4000 loss 5.827777 loss_att 4.988527 loss_ctc 7.786026 lr 0.00009525 rank 0
2023-07-06 00:48:37,802 DEBUG TRAIN Batch 21/5000 loss 4.653869 loss_att 4.069042 loss_ctc 6.018464 lr 0.00009523 rank 0
2023-07-06 00:48:37,814 DEBUG TRAIN Batch 21/5000 loss 5.219382 loss_att 4.489801 loss_ctc 6.921737 lr 0.00009523 rank 1
2023-07-06 00:52:37,638 DEBUG TRAIN Batch 21/6000 loss 7.086477 loss_att 5.723207 loss_ctc 10.267439 lr 0.00009521 rank 1
2023-07-06 00:52:37,642 DEBUG TRAIN Batch 21/6000 loss 3.079601 loss_att 2.545566 loss_ctc 4.325682 lr 0.00009522 rank 0
2023-07-06 00:56:41,776 DEBUG TRAIN Batch 21/7000 loss 2.255513 loss_att 1.992698 loss_ctc 2.868748 lr 0.00009520 rank 1
2023-07-06 00:56:41,780 DEBUG TRAIN Batch 21/7000 loss 3.478504 loss_att 2.759994 loss_ctc 5.155029 lr 0.00009520 rank 0
2023-07-06 01:00:43,595 DEBUG TRAIN Batch 21/8000 loss 5.083906 loss_att 4.445230 loss_ctc 6.574150 lr 0.00009518 rank 1
2023-07-06 01:00:43,597 DEBUG TRAIN Batch 21/8000 loss 2.628749 loss_att 2.301361 loss_ctc 3.392657 lr 0.00009518 rank 0
2023-07-06 01:04:49,351 DEBUG TRAIN Batch 21/9000 loss 2.449564 loss_att 2.088454 loss_ctc 3.292152 lr 0.00009516 rank 1
2023-07-06 01:04:49,352 DEBUG TRAIN Batch 21/9000 loss 8.448221 loss_att 6.836964 loss_ctc 12.207821 lr 0.00009516 rank 0
2023-07-06 01:06:26,055 WARNING NaN or Inf found in input tensor.
2023-07-06 01:08:57,141 DEBUG TRAIN Batch 21/10000 loss 2.527137 loss_att 2.135646 loss_ctc 3.440617 lr 0.00009515 rank 1
2023-07-06 01:08:57,143 DEBUG TRAIN Batch 21/10000 loss 5.353492 loss_att 4.443207 loss_ctc 7.477491 lr 0.00009515 rank 0
2023-07-06 01:13:15,114 DEBUG TRAIN Batch 21/11000 loss 3.949065 loss_att 3.131847 loss_ctc 5.855907 lr 0.00009513 rank 1
2023-07-06 01:13:15,114 DEBUG TRAIN Batch 21/11000 loss 5.867299 loss_att 4.497297 loss_ctc 9.063969 lr 0.00009513 rank 0
2023-07-06 01:17:19,926 DEBUG TRAIN Batch 21/12000 loss 2.541307 loss_att 2.093285 loss_ctc 3.586691 lr 0.00009511 rank 0
2023-07-06 01:17:19,952 DEBUG TRAIN Batch 21/12000 loss 5.046651 loss_att 3.890333 loss_ctc 7.744726 lr 0.00009511 rank 1
2023-07-06 01:21:27,448 DEBUG TRAIN Batch 21/13000 loss 3.464473 loss_att 2.908300 loss_ctc 4.762211 lr 0.00009510 rank 0
2023-07-06 01:21:27,449 DEBUG TRAIN Batch 21/13000 loss 3.908317 loss_att 3.192776 loss_ctc 5.577912 lr 0.00009509 rank 1
2023-07-06 01:25:36,563 DEBUG TRAIN Batch 21/14000 loss 4.107967 loss_att 3.242220 loss_ctc 6.128043 lr 0.00009508 rank 1
2023-07-06 01:25:36,565 DEBUG TRAIN Batch 21/14000 loss 6.001060 loss_att 5.048103 loss_ctc 8.224626 lr 0.00009508 rank 0
2023-07-06 01:29:47,101 DEBUG TRAIN Batch 21/15000 loss 4.284321 loss_att 3.380858 loss_ctc 6.392400 lr 0.00009506 rank 0
2023-07-06 01:29:47,125 DEBUG TRAIN Batch 21/15000 loss 4.095475 loss_att 3.608955 loss_ctc 5.230690 lr 0.00009506 rank 1
2023-07-06 01:33:56,977 DEBUG TRAIN Batch 21/16000 loss 4.564499 loss_att 3.704413 loss_ctc 6.571367 lr 0.00009504 rank 0
2023-07-06 01:33:57,000 DEBUG TRAIN Batch 21/16000 loss 6.637702 loss_att 5.713584 loss_ctc 8.793977 lr 0.00009504 rank 1
2023-07-06 01:38:11,410 DEBUG TRAIN Batch 21/17000 loss 4.200289 loss_att 3.315360 loss_ctc 6.265124 lr 0.00009503 rank 1
2023-07-06 01:38:11,467 DEBUG TRAIN Batch 21/17000 loss 7.756152 loss_att 6.477894 loss_ctc 10.738754 lr 0.00009503 rank 0
2023-07-06 01:42:16,846 DEBUG TRAIN Batch 21/18000 loss 10.695209 loss_att 8.445811 loss_ctc 15.943801 lr 0.00009501 rank 0
2023-07-06 01:42:16,846 DEBUG TRAIN Batch 21/18000 loss 2.017428 loss_att 1.607271 loss_ctc 2.974463 lr 0.00009501 rank 1
2023-07-06 01:46:22,252 DEBUG TRAIN Batch 21/19000 loss 5.787153 loss_att 4.617088 loss_ctc 8.517306 lr 0.00009499 rank 1
2023-07-06 01:46:22,260 DEBUG TRAIN Batch 21/19000 loss 6.580645 loss_att 5.522427 loss_ctc 9.049820 lr 0.00009499 rank 0
2023-07-06 01:50:28,311 DEBUG TRAIN Batch 21/20000 loss 3.093055 loss_att 2.523408 loss_ctc 4.422234 lr 0.00009497 rank 1
2023-07-06 01:50:28,317 DEBUG TRAIN Batch 21/20000 loss 4.512797 loss_att 3.941962 loss_ctc 5.844744 lr 0.00009498 rank 0
2023-07-06 01:54:30,893 DEBUG TRAIN Batch 21/21000 loss 4.160145 loss_att 3.388479 loss_ctc 5.960700 lr 0.00009496 rank 1
2023-07-06 01:54:30,921 DEBUG TRAIN Batch 21/21000 loss 5.097179 loss_att 4.201762 loss_ctc 7.186487 lr 0.00009496 rank 0
2023-07-06 01:58:53,468 DEBUG TRAIN Batch 21/22000 loss 9.489947 loss_att 7.575356 loss_ctc 13.957329 lr 0.00009494 rank 1
2023-07-06 01:58:53,470 DEBUG TRAIN Batch 21/22000 loss 3.753104 loss_att 3.201078 loss_ctc 5.041163 lr 0.00009494 rank 0
2023-07-06 02:03:03,342 DEBUG TRAIN Batch 21/23000 loss 4.642207 loss_att 3.794558 loss_ctc 6.620054 lr 0.00009492 rank 1
2023-07-06 02:03:03,343 DEBUG TRAIN Batch 21/23000 loss 3.458442 loss_att 2.955771 loss_ctc 4.631342 lr 0.00009492 rank 0
2023-07-06 02:07:12,128 DEBUG TRAIN Batch 21/24000 loss 2.365747 loss_att 1.986854 loss_ctc 3.249830 lr 0.00009491 rank 1
2023-07-06 02:07:12,130 DEBUG TRAIN Batch 21/24000 loss 2.556603 loss_att 2.128428 loss_ctc 3.555679 lr 0.00009491 rank 0
2023-07-06 02:11:23,005 DEBUG TRAIN Batch 21/25000 loss 6.009993 loss_att 5.065679 loss_ctc 8.213392 lr 0.00009489 rank 1
2023-07-06 02:11:23,026 DEBUG TRAIN Batch 21/25000 loss 5.100394 loss_att 4.317134 loss_ctc 6.928001 lr 0.00009489 rank 0
2023-07-06 02:15:25,936 DEBUG TRAIN Batch 21/26000 loss 2.686372 loss_att 2.292954 loss_ctc 3.604348 lr 0.00009487 rank 1
2023-07-06 02:15:25,937 DEBUG TRAIN Batch 21/26000 loss 6.729273 loss_att 5.810558 loss_ctc 8.872941 lr 0.00009487 rank 0
2023-07-06 02:19:36,110 DEBUG TRAIN Batch 21/27000 loss 2.653066 loss_att 2.250395 loss_ctc 3.592632 lr 0.00009485 rank 1
2023-07-06 02:19:36,111 DEBUG TRAIN Batch 21/27000 loss 6.421010 loss_att 5.342762 loss_ctc 8.936923 lr 0.00009486 rank 0
2023-07-06 02:23:48,290 DEBUG TRAIN Batch 21/28000 loss 3.957359 loss_att 3.209248 loss_ctc 5.702950 lr 0.00009484 rank 0
2023-07-06 02:23:48,290 DEBUG TRAIN Batch 21/28000 loss 4.507279 loss_att 3.742880 loss_ctc 6.290878 lr 0.00009484 rank 1
2023-07-06 02:27:58,141 DEBUG TRAIN Batch 21/29000 loss 7.025519 loss_att 6.122022 loss_ctc 9.133679 lr 0.00009482 rank 1
2023-07-06 02:27:58,144 DEBUG TRAIN Batch 21/29000 loss 6.503027 loss_att 5.501109 loss_ctc 8.840836 lr 0.00009482 rank 0
2023-07-06 02:32:11,858 DEBUG TRAIN Batch 21/30000 loss 4.882383 loss_att 4.121054 loss_ctc 6.658819 lr 0.00009480 rank 1
2023-07-06 02:32:11,885 DEBUG TRAIN Batch 21/30000 loss 4.966660 loss_att 4.157302 loss_ctc 6.855164 lr 0.00009480 rank 0
2023-07-06 02:36:19,393 DEBUG TRAIN Batch 21/31000 loss 4.001994 loss_att 3.283526 loss_ctc 5.678418 lr 0.00009479 rank 1
2023-07-06 02:36:19,395 DEBUG TRAIN Batch 21/31000 loss 2.599795 loss_att 2.143716 loss_ctc 3.663980 lr 0.00009479 rank 0
2023-07-06 02:40:24,301 DEBUG TRAIN Batch 21/32000 loss 1.714032 loss_att 1.531076 loss_ctc 2.140929 lr 0.00009477 rank 1
2023-07-06 02:40:24,303 DEBUG TRAIN Batch 21/32000 loss 2.651725 loss_att 2.185066 loss_ctc 3.740596 lr 0.00009477 rank 0
2023-07-06 02:44:34,618 DEBUG TRAIN Batch 21/33000 loss 2.725056 loss_att 2.169055 loss_ctc 4.022390 lr 0.00009475 rank 1
2023-07-06 02:44:34,623 DEBUG TRAIN Batch 21/33000 loss 9.487046 loss_att 8.155991 loss_ctc 12.592844 lr 0.00009475 rank 0
2023-07-06 02:48:49,079 DEBUG TRAIN Batch 21/34000 loss 4.713434 loss_att 4.046107 loss_ctc 6.270530 lr 0.00009474 rank 1
2023-07-06 02:48:49,083 DEBUG TRAIN Batch 21/34000 loss 5.775210 loss_att 5.171380 loss_ctc 7.184147 lr 0.00009474 rank 0
2023-07-06 02:52:58,632 DEBUG TRAIN Batch 21/35000 loss 2.157527 loss_att 1.840707 loss_ctc 2.896775 lr 0.00009472 rank 1
2023-07-06 02:52:58,633 DEBUG TRAIN Batch 21/35000 loss 4.231088 loss_att 3.816595 loss_ctc 5.198236 lr 0.00009472 rank 0
2023-07-06 02:57:00,420 DEBUG TRAIN Batch 21/36000 loss 4.617448 loss_att 3.843291 loss_ctc 6.423814 lr 0.00009470 rank 1
2023-07-06 02:57:00,427 DEBUG TRAIN Batch 21/36000 loss 3.953654 loss_att 3.446633 loss_ctc 5.136701 lr 0.00009470 rank 0
2023-07-06 03:01:09,804 DEBUG TRAIN Batch 21/37000 loss 8.072706 loss_att 6.955674 loss_ctc 10.679113 lr 0.00009469 rank 0
2023-07-06 03:01:09,833 DEBUG TRAIN Batch 21/37000 loss 8.576206 loss_att 6.825466 loss_ctc 12.661265 lr 0.00009468 rank 1
2023-07-06 03:05:15,412 DEBUG TRAIN Batch 21/38000 loss 4.166201 loss_att 3.313750 loss_ctc 6.155252 lr 0.00009467 rank 1
2023-07-06 03:05:15,414 DEBUG TRAIN Batch 21/38000 loss 4.002272 loss_att 3.456426 loss_ctc 5.275911 lr 0.00009467 rank 0
2023-07-06 03:07:53,279 WARNING NaN or Inf found in input tensor.
2023-07-06 03:09:22,105 DEBUG TRAIN Batch 21/39000 loss 1.971193 loss_att 1.665221 loss_ctc 2.685127 lr 0.00009465 rank 1
2023-07-06 03:09:22,107 DEBUG TRAIN Batch 21/39000 loss 7.037410 loss_att 5.447486 loss_ctc 10.747231 lr 0.00009465 rank 0
2023-07-06 03:13:31,624 DEBUG TRAIN Batch 21/40000 loss 3.105947 loss_att 2.899237 loss_ctc 3.588273 lr 0.00009463 rank 1
2023-07-06 03:13:31,631 DEBUG TRAIN Batch 21/40000 loss 4.837585 loss_att 4.246374 loss_ctc 6.217076 lr 0.00009463 rank 0
2023-07-06 03:17:51,535 DEBUG TRAIN Batch 21/41000 loss 2.016353 loss_att 1.827426 loss_ctc 2.457182 lr 0.00009462 rank 0
2023-07-06 03:17:51,536 DEBUG TRAIN Batch 21/41000 loss 6.565642 loss_att 5.357475 loss_ctc 9.384698 lr 0.00009462 rank 1
2023-07-06 03:22:03,400 DEBUG TRAIN Batch 21/42000 loss 5.435697 loss_att 4.364351 loss_ctc 7.935503 lr 0.00009460 rank 1
2023-07-06 03:22:03,402 DEBUG TRAIN Batch 21/42000 loss 4.003239 loss_att 3.481857 loss_ctc 5.219795 lr 0.00009460 rank 0
2023-07-06 03:22:07,821 WARNING NaN or Inf found in input tensor.
2023-07-06 03:26:04,248 DEBUG TRAIN Batch 21/43000 loss 2.169159 loss_att 1.863489 loss_ctc 2.882390 lr 0.00009458 rank 1
2023-07-06 03:26:04,252 DEBUG TRAIN Batch 21/43000 loss 1.205925 loss_att 1.014152 loss_ctc 1.653396 lr 0.00009458 rank 0
2023-07-06 03:30:18,095 DEBUG TRAIN Batch 21/44000 loss 5.117885 loss_att 4.150050 loss_ctc 7.376166 lr 0.00009457 rank 1
2023-07-06 03:30:18,098 DEBUG TRAIN Batch 21/44000 loss 3.431921 loss_att 2.973465 loss_ctc 4.501652 lr 0.00009457 rank 0
wav: Premature EOF on .wav input file
2023-07-06 03:34:16,208 DEBUG TRAIN Batch 21/45000 loss 3.120217 loss_att 2.540414 loss_ctc 4.473090 lr 0.00009455 rank 1
2023-07-06 03:34:16,212 DEBUG TRAIN Batch 21/45000 loss 6.043803 loss_att 5.068311 loss_ctc 8.319952 lr 0.00009455 rank 0
2023-07-06 03:38:21,941 DEBUG TRAIN Batch 21/46000 loss 7.283265 loss_att 5.986020 loss_ctc 10.310172 lr 0.00009453 rank 1
2023-07-06 03:38:21,944 DEBUG TRAIN Batch 21/46000 loss 6.197315 loss_att 5.209415 loss_ctc 8.502415 lr 0.00009453 rank 0
2023-07-06 03:42:27,814 DEBUG TRAIN Batch 21/47000 loss 4.486290 loss_att 3.385488 loss_ctc 7.054829 lr 0.00009452 rank 0
2023-07-06 03:42:27,815 DEBUG TRAIN Batch 21/47000 loss 3.748131 loss_att 3.247482 loss_ctc 4.916311 lr 0.00009451 rank 1
2023-07-06 03:46:32,953 DEBUG TRAIN Batch 21/48000 loss 3.205446 loss_att 2.656221 loss_ctc 4.486969 lr 0.00009450 rank 1
2023-07-06 03:46:32,958 DEBUG TRAIN Batch 21/48000 loss 5.028229 loss_att 4.191675 loss_ctc 6.980187 lr 0.00009450 rank 0
2023-07-06 03:50:43,364 DEBUG TRAIN Batch 21/49000 loss 7.589616 loss_att 6.112076 loss_ctc 11.037210 lr 0.00009448 rank 0
2023-07-06 03:50:43,394 DEBUG TRAIN Batch 21/49000 loss 2.876911 loss_att 2.427701 loss_ctc 3.925066 lr 0.00009448 rank 1
2023-07-06 03:54:59,297 DEBUG TRAIN Batch 21/50000 loss 1.596706 loss_att 1.400997 loss_ctc 2.053361 lr 0.00009446 rank 1
2023-07-06 03:54:59,302 DEBUG TRAIN Batch 21/50000 loss 3.876009 loss_att 3.273497 loss_ctc 5.281871 lr 0.00009447 rank 0
2023-07-06 03:59:02,121 DEBUG TRAIN Batch 21/51000 loss 2.515823 loss_att 2.258065 loss_ctc 3.117260 lr 0.00009445 rank 1
2023-07-06 03:59:02,123 DEBUG TRAIN Batch 21/51000 loss 2.648257 loss_att 2.254384 loss_ctc 3.567295 lr 0.00009445 rank 0
2023-07-06 04:02:50,563 WARNING NaN or Inf found in input tensor.
2023-07-06 04:03:14,088 DEBUG TRAIN Batch 21/52000 loss 3.555564 loss_att 3.064575 loss_ctc 4.701207 lr 0.00009443 rank 0
2023-07-06 04:03:14,090 DEBUG TRAIN Batch 21/52000 loss 3.935208 loss_att 3.164884 loss_ctc 5.732631 lr 0.00009443 rank 1
2023-07-06 04:07:23,905 DEBUG TRAIN Batch 21/53000 loss 4.096784 loss_att 3.624498 loss_ctc 5.198784 lr 0.00009442 rank 0
2023-07-06 04:07:23,939 DEBUG TRAIN Batch 21/53000 loss 5.071715 loss_att 4.224724 loss_ctc 7.048027 lr 0.00009441 rank 1
2023-07-06 04:11:30,244 DEBUG TRAIN Batch 21/54000 loss 5.815730 loss_att 4.841247 loss_ctc 8.089523 lr 0.00009440 rank 0
2023-07-06 04:11:30,245 DEBUG TRAIN Batch 21/54000 loss 6.626072 loss_att 5.180572 loss_ctc 9.998905 lr 0.00009440 rank 1
2023-07-06 04:15:50,941 DEBUG TRAIN Batch 21/55000 loss 4.989743 loss_att 4.368965 loss_ctc 6.438224 lr 0.00009438 rank 1
2023-07-06 04:15:50,942 DEBUG TRAIN Batch 21/55000 loss 5.101486 loss_att 4.176877 loss_ctc 7.258909 lr 0.00009438 rank 0
2023-07-06 04:20:02,371 DEBUG TRAIN Batch 21/56000 loss 3.447250 loss_att 2.957156 loss_ctc 4.590804 lr 0.00009436 rank 1
2023-07-06 04:20:02,377 DEBUG TRAIN Batch 21/56000 loss 1.926790 loss_att 1.701270 loss_ctc 2.453002 lr 0.00009436 rank 0
2023-07-06 04:24:07,805 DEBUG TRAIN Batch 21/57000 loss 5.005487 loss_att 4.343098 loss_ctc 6.551063 lr 0.00009435 rank 0
2023-07-06 04:24:07,805 DEBUG TRAIN Batch 21/57000 loss 4.112299 loss_att 3.449254 loss_ctc 5.659404 lr 0.00009435 rank 1
2023-07-06 04:28:11,960 DEBUG TRAIN Batch 21/58000 loss 5.472651 loss_att 4.559671 loss_ctc 7.602938 lr 0.00009433 rank 1
2023-07-06 04:28:11,965 DEBUG TRAIN Batch 21/58000 loss 5.741564 loss_att 4.804999 loss_ctc 7.926881 lr 0.00009433 rank 0
2023-07-06 04:32:22,249 DEBUG TRAIN Batch 21/59000 loss 4.393636 loss_att 3.743452 loss_ctc 5.910730 lr 0.00009431 rank 1
2023-07-06 04:32:22,251 DEBUG TRAIN Batch 21/59000 loss 5.585433 loss_att 4.686626 loss_ctc 7.682650 lr 0.00009431 rank 0
2023-07-06 04:36:28,241 DEBUG TRAIN Batch 21/60000 loss 7.100108 loss_att 6.071058 loss_ctc 9.501225 lr 0.00009430 rank 1
2023-07-06 04:36:28,242 DEBUG TRAIN Batch 21/60000 loss 6.072709 loss_att 5.104979 loss_ctc 8.330746 lr 0.00009430 rank 0
2023-07-06 04:40:27,523 DEBUG TRAIN Batch 21/61000 loss 3.594366 loss_att 3.069553 loss_ctc 4.818929 lr 0.00009428 rank 1
2023-07-06 04:40:27,523 DEBUG TRAIN Batch 21/61000 loss 3.597381 loss_att 2.925402 loss_ctc 5.165333 lr 0.00009428 rank 0
2023-07-06 04:44:14,752 WARNING NaN or Inf found in input tensor.
2023-07-06 04:44:40,879 DEBUG TRAIN Batch 21/62000 loss 4.585691 loss_att 3.691655 loss_ctc 6.671774 lr 0.00009426 rank 1
2023-07-06 04:44:40,881 DEBUG TRAIN Batch 21/62000 loss 4.984576 loss_att 4.329373 loss_ctc 6.513382 lr 0.00009426 rank 0
2023-07-06 04:48:42,147 DEBUG TRAIN Batch 21/63000 loss 2.295883 loss_att 1.955755 loss_ctc 3.089514 lr 0.00009425 rank 0
2023-07-06 04:48:42,147 DEBUG TRAIN Batch 21/63000 loss 4.779286 loss_att 4.176592 loss_ctc 6.185574 lr 0.00009425 rank 1
2023-07-06 04:52:50,581 DEBUG TRAIN Batch 21/64000 loss 5.851690 loss_att 4.420052 loss_ctc 9.192179 lr 0.00009423 rank 1
2023-07-06 04:52:50,581 DEBUG TRAIN Batch 21/64000 loss 2.267581 loss_att 1.958800 loss_ctc 2.988069 lr 0.00009423 rank 0
2023-07-06 04:57:05,212 DEBUG TRAIN Batch 21/65000 loss 7.647229 loss_att 6.238581 loss_ctc 10.934076 lr 0.00009421 rank 1
2023-07-06 04:57:05,215 DEBUG TRAIN Batch 21/65000 loss 4.313495 loss_att 3.717655 loss_ctc 5.703786 lr 0.00009421 rank 0
2023-07-06 05:01:15,390 DEBUG TRAIN Batch 21/66000 loss 2.500629 loss_att 2.080942 loss_ctc 3.479899 lr 0.00009420 rank 1
2023-07-06 05:01:15,420 DEBUG TRAIN Batch 21/66000 loss 5.493689 loss_att 4.392280 loss_ctc 8.063643 lr 0.00009420 rank 0
2023-07-06 05:05:17,290 DEBUG TRAIN Batch 21/67000 loss 6.085415 loss_att 5.209741 loss_ctc 8.128656 lr 0.00009418 rank 0
2023-07-06 05:05:17,291 DEBUG TRAIN Batch 21/67000 loss 2.874617 loss_att 2.400161 loss_ctc 3.981681 lr 0.00009418 rank 1
2023-07-06 05:09:28,089 DEBUG TRAIN Batch 21/68000 loss 3.816724 loss_att 3.358940 loss_ctc 4.884885 lr 0.00009416 rank 1
2023-07-06 05:09:28,117 DEBUG TRAIN Batch 21/68000 loss 6.667646 loss_att 5.780426 loss_ctc 8.737827 lr 0.00009416 rank 0
2023-07-06 05:13:33,170 DEBUG TRAIN Batch 21/69000 loss 6.007311 loss_att 4.957320 loss_ctc 8.457289 lr 0.00009415 rank 1
2023-07-06 05:13:33,171 DEBUG TRAIN Batch 21/69000 loss 3.434531 loss_att 2.802180 loss_ctc 4.910018 lr 0.00009415 rank 0
2023-07-06 05:17:40,858 DEBUG TRAIN Batch 21/70000 loss 5.002335 loss_att 4.214121 loss_ctc 6.841500 lr 0.00009413 rank 0
2023-07-06 05:17:40,860 DEBUG TRAIN Batch 21/70000 loss 5.508765 loss_att 4.492166 loss_ctc 7.880831 lr 0.00009413 rank 1
2023-07-06 05:21:52,495 DEBUG TRAIN Batch 21/71000 loss 6.085181 loss_att 5.135329 loss_ctc 8.301502 lr 0.00009411 rank 1
2023-07-06 05:21:52,497 DEBUG TRAIN Batch 21/71000 loss 8.127642 loss_att 6.491178 loss_ctc 11.946056 lr 0.00009411 rank 0
2023-07-06 05:26:06,170 DEBUG TRAIN Batch 21/72000 loss 1.309796 loss_att 1.148466 loss_ctc 1.686233 lr 0.00009410 rank 0
2023-07-06 05:26:06,199 DEBUG TRAIN Batch 21/72000 loss 3.375537 loss_att 3.012629 loss_ctc 4.222324 lr 0.00009410 rank 1
2023-07-06 05:30:12,551 DEBUG TRAIN Batch 21/73000 loss 5.505230 loss_att 4.715917 loss_ctc 7.346962 lr 0.00009408 rank 1
2023-07-06 05:30:12,553 DEBUG TRAIN Batch 21/73000 loss 2.096792 loss_att 1.849861 loss_ctc 2.672964 lr 0.00009408 rank 0
2023-07-06 05:34:23,146 DEBUG TRAIN Batch 21/74000 loss 6.318251 loss_att 5.241016 loss_ctc 8.831797 lr 0.00009406 rank 0
2023-07-06 05:34:23,178 DEBUG TRAIN Batch 21/74000 loss 4.373919 loss_att 3.543794 loss_ctc 6.310879 lr 0.00009406 rank 1
2023-07-06 05:36:53,213 WARNING NaN or Inf found in input tensor.
2023-07-06 05:38:42,772 DEBUG TRAIN Batch 21/75000 loss 3.949409 loss_att 3.205328 loss_ctc 5.685596 lr 0.00009405 rank 1
2023-07-06 05:38:42,774 DEBUG TRAIN Batch 21/75000 loss 4.573146 loss_att 4.318281 loss_ctc 5.167831 lr 0.00009405 rank 0
2023-07-06 05:42:44,282 DEBUG TRAIN Batch 21/76000 loss 3.884068 loss_att 3.047674 loss_ctc 5.835656 lr 0.00009403 rank 1
2023-07-06 05:42:44,288 DEBUG TRAIN Batch 21/76000 loss 3.987097 loss_att 3.327356 loss_ctc 5.526491 lr 0.00009403 rank 0
2023-07-06 05:46:40,658 DEBUG TRAIN Batch 21/77000 loss 4.954045 loss_att 4.106632 loss_ctc 6.931341 lr 0.00009401 rank 1
2023-07-06 05:46:40,662 DEBUG TRAIN Batch 21/77000 loss 2.765541 loss_att 2.316845 loss_ctc 3.812497 lr 0.00009401 rank 0
2023-07-06 05:48:51,169 WARNING NaN or Inf found in input tensor.
2023-07-06 05:50:55,401 DEBUG TRAIN Batch 21/78000 loss 9.076157 loss_att 7.159639 loss_ctc 13.548031 lr 0.00009400 rank 1
2023-07-06 05:50:55,402 DEBUG TRAIN Batch 21/78000 loss 2.279245 loss_att 1.916853 loss_ctc 3.124827 lr 0.00009400 rank 0
2023-07-06 05:55:00,563 DEBUG TRAIN Batch 21/79000 loss 4.398593 loss_att 3.481499 loss_ctc 6.538478 lr 0.00009398 rank 0
2023-07-06 05:55:00,563 DEBUG TRAIN Batch 21/79000 loss 8.197729 loss_att 6.813385 loss_ctc 11.427864 lr 0.00009398 rank 1
2023-07-06 05:59:25,811 DEBUG TRAIN Batch 21/80000 loss 3.909489 loss_att 3.235188 loss_ctc 5.482855 lr 0.00009396 rank 0
2023-07-06 05:59:25,813 DEBUG TRAIN Batch 21/80000 loss 7.183246 loss_att 6.033839 loss_ctc 9.865196 lr 0.00009396 rank 1
2023-07-06 06:03:33,750 DEBUG TRAIN Batch 21/81000 loss 2.831822 loss_att 2.615812 loss_ctc 3.335845 lr 0.00009395 rank 0
2023-07-06 06:03:33,750 DEBUG TRAIN Batch 21/81000 loss 3.373077 loss_att 2.835919 loss_ctc 4.626448 lr 0.00009395 rank 1
2023-07-06 06:07:41,575 DEBUG TRAIN Batch 21/82000 loss 4.223273 loss_att 3.510890 loss_ctc 5.885500 lr 0.00009393 rank 1
2023-07-06 06:07:41,576 DEBUG TRAIN Batch 21/82000 loss 6.466152 loss_att 5.441957 loss_ctc 8.855942 lr 0.00009393 rank 0
2023-07-06 06:11:47,652 DEBUG TRAIN Batch 21/83000 loss 3.289140 loss_att 2.852909 loss_ctc 4.307014 lr 0.00009391 rank 0
2023-07-06 06:11:47,687 DEBUG TRAIN Batch 21/83000 loss 3.685705 loss_att 3.015450 loss_ctc 5.249634 lr 0.00009391 rank 1
2023-07-06 06:16:00,210 DEBUG TRAIN Batch 21/84000 loss 3.110468 loss_att 2.774378 loss_ctc 3.894678 lr 0.00009390 rank 1
2023-07-06 06:16:00,211 DEBUG TRAIN Batch 21/84000 loss 8.618459 loss_att 7.245985 loss_ctc 11.820898 lr 0.00009390 rank 0
2023-07-06 06:20:06,039 DEBUG TRAIN Batch 21/85000 loss 4.406575 loss_att 3.647707 loss_ctc 6.177266 lr 0.00009388 rank 1
2023-07-06 06:20:06,042 DEBUG TRAIN Batch 21/85000 loss 4.807734 loss_att 4.070423 loss_ctc 6.528128 lr 0.00009388 rank 0
2023-07-06 06:24:10,970 DEBUG TRAIN Batch 21/86000 loss 4.103356 loss_att 3.244847 loss_ctc 6.106543 lr 0.00009386 rank 1
2023-07-06 06:24:10,977 DEBUG TRAIN Batch 21/86000 loss 4.936326 loss_att 3.941585 loss_ctc 7.257386 lr 0.00009386 rank 0
2023-07-06 06:28:18,960 DEBUG TRAIN Batch 21/87000 loss 6.940073 loss_att 5.634148 loss_ctc 9.987233 lr 0.00009385 rank 0
2023-07-06 06:28:18,962 DEBUG TRAIN Batch 21/87000 loss 6.260898 loss_att 5.311321 loss_ctc 8.476577 lr 0.00009385 rank 1
2023-07-06 06:30:26,809 WARNING NaN or Inf found in input tensor.
2023-07-06 06:32:34,348 DEBUG TRAIN Batch 21/88000 loss 9.055996 loss_att 7.815535 loss_ctc 11.950403 lr 0.00009383 rank 0
2023-07-06 06:32:34,348 DEBUG TRAIN Batch 21/88000 loss 5.104969 loss_att 4.333908 loss_ctc 6.904110 lr 0.00009383 rank 1
2023-07-06 06:36:49,851 DEBUG TRAIN Batch 21/89000 loss 6.824234 loss_att 5.995748 loss_ctc 8.757369 lr 0.00009381 rank 1
2023-07-06 06:36:49,854 DEBUG TRAIN Batch 21/89000 loss 4.725151 loss_att 4.236049 loss_ctc 5.866388 lr 0.00009382 rank 0
2023-07-06 06:41:00,416 DEBUG TRAIN Batch 21/90000 loss 3.343622 loss_att 2.892889 loss_ctc 4.395331 lr 0.00009380 rank 1
2023-07-06 06:41:00,420 DEBUG TRAIN Batch 21/90000 loss 3.923841 loss_att 3.205614 loss_ctc 5.599704 lr 0.00009380 rank 0
2023-07-06 06:45:09,410 DEBUG TRAIN Batch 21/91000 loss 3.870577 loss_att 3.116056 loss_ctc 5.631125 lr 0.00009378 rank 1
2023-07-06 06:45:09,412 DEBUG TRAIN Batch 21/91000 loss 7.154148 loss_att 6.162195 loss_ctc 9.468706 lr 0.00009378 rank 0
2023-07-06 06:49:22,735 DEBUG TRAIN Batch 21/92000 loss 7.004538 loss_att 5.921649 loss_ctc 9.531278 lr 0.00009376 rank 1
2023-07-06 06:49:22,764 DEBUG TRAIN Batch 21/92000 loss 1.895631 loss_att 1.703588 loss_ctc 2.343732 lr 0.00009377 rank 0
2023-07-06 06:53:34,885 DEBUG TRAIN Batch 21/93000 loss 3.092803 loss_att 2.425785 loss_ctc 4.649178 lr 0.00009375 rank 1
2023-07-06 06:53:34,886 DEBUG TRAIN Batch 21/93000 loss 4.975110 loss_att 4.245193 loss_ctc 6.678247 lr 0.00009375 rank 0
2023-07-06 06:57:44,531 DEBUG TRAIN Batch 21/94000 loss 2.696238 loss_att 2.136373 loss_ctc 4.002589 lr 0.00009373 rank 1
2023-07-06 06:57:44,539 DEBUG TRAIN Batch 21/94000 loss 3.513365 loss_att 2.988071 loss_ctc 4.739051 lr 0.00009373 rank 0
2023-07-06 07:01:44,243 DEBUG TRAIN Batch 21/95000 loss 4.729334 loss_att 4.006836 loss_ctc 6.415162 lr 0.00009371 rank 1
2023-07-06 07:01:44,244 DEBUG TRAIN Batch 21/95000 loss 4.432081 loss_att 3.697177 loss_ctc 6.146857 lr 0.00009372 rank 0
2023-07-06 07:05:49,571 DEBUG TRAIN Batch 21/96000 loss 8.479766 loss_att 6.839563 loss_ctc 12.306904 lr 0.00009370 rank 1
2023-07-06 07:05:49,574 DEBUG TRAIN Batch 21/96000 loss 2.593398 loss_att 2.235366 loss_ctc 3.428805 lr 0.00009370 rank 0
2023-07-06 07:10:02,279 DEBUG TRAIN Batch 21/97000 loss 4.642175 loss_att 3.786402 loss_ctc 6.638977 lr 0.00009368 rank 1
2023-07-06 07:10:02,285 DEBUG TRAIN Batch 21/97000 loss 6.490880 loss_att 5.469820 loss_ctc 8.873354 lr 0.00009368 rank 0
2023-07-06 07:14:07,405 DEBUG TRAIN Batch 21/98000 loss 7.402611 loss_att 6.119589 loss_ctc 10.396329 lr 0.00009367 rank 1
2023-07-06 07:14:07,407 DEBUG TRAIN Batch 21/98000 loss 3.862004 loss_att 3.133271 loss_ctc 5.562383 lr 0.00009367 rank 0
2023-07-06 07:18:18,607 DEBUG TRAIN Batch 21/99000 loss 3.414098 loss_att 3.010785 loss_ctc 4.355162 lr 0.00009365 rank 0
2023-07-06 07:18:18,630 DEBUG TRAIN Batch 21/99000 loss 3.126454 loss_att 2.575860 loss_ctc 4.411175 lr 0.00009365 rank 1
2023-07-06 07:22:31,947 DEBUG TRAIN Batch 21/100000 loss 4.516481 loss_att 3.413382 loss_ctc 7.090381 lr 0.00009363 rank 1
2023-07-06 07:22:31,948 DEBUG TRAIN Batch 21/100000 loss 2.879164 loss_att 2.577763 loss_ctc 3.582430 lr 0.00009363 rank 0
2023-07-06 07:26:31,240 DEBUG TRAIN Batch 21/101000 loss 5.313863 loss_att 4.487730 loss_ctc 7.241505 lr 0.00009362 rank 0
2023-07-06 07:26:31,240 DEBUG TRAIN Batch 21/101000 loss 5.313353 loss_att 4.633419 loss_ctc 6.899865 lr 0.00009362 rank 1
2023-07-06 07:30:37,081 DEBUG TRAIN Batch 21/102000 loss 3.940046 loss_att 3.269663 loss_ctc 5.504274 lr 0.00009360 rank 1
2023-07-06 07:30:37,085 DEBUG TRAIN Batch 21/102000 loss 4.689388 loss_att 4.071651 loss_ctc 6.130773 lr 0.00009360 rank 0
2023-07-06 07:34:47,792 DEBUG TRAIN Batch 21/103000 loss 4.463058 loss_att 3.903708 loss_ctc 5.768210 lr 0.00009358 rank 1
2023-07-06 07:34:47,793 DEBUG TRAIN Batch 21/103000 loss 3.580380 loss_att 3.034162 loss_ctc 4.854891 lr 0.00009358 rank 0
2023-07-06 07:39:08,976 DEBUG TRAIN Batch 21/104000 loss 3.884299 loss_att 3.369272 loss_ctc 5.086027 lr 0.00009357 rank 0
2023-07-06 07:39:08,998 DEBUG TRAIN Batch 21/104000 loss 8.953339 loss_att 7.821348 loss_ctc 11.594648 lr 0.00009357 rank 1
2023-07-06 07:43:21,744 DEBUG TRAIN Batch 21/105000 loss 5.132559 loss_att 3.858024 loss_ctc 8.106474 lr 0.00009355 rank 1
2023-07-06 07:43:21,748 DEBUG TRAIN Batch 21/105000 loss 3.686282 loss_att 3.090916 loss_ctc 5.075469 lr 0.00009355 rank 0
2023-07-06 07:47:27,507 DEBUG TRAIN Batch 21/106000 loss 3.607507 loss_att 2.926264 loss_ctc 5.197073 lr 0.00009354 rank 0
2023-07-06 07:47:27,508 DEBUG TRAIN Batch 21/106000 loss 3.620611 loss_att 3.116382 loss_ctc 4.797145 lr 0.00009353 rank 1
2023-07-06 07:51:31,673 DEBUG TRAIN Batch 21/107000 loss 4.514864 loss_att 3.745066 loss_ctc 6.311060 lr 0.00009352 rank 1
2023-07-06 07:51:31,677 DEBUG TRAIN Batch 21/107000 loss 5.734072 loss_att 4.722374 loss_ctc 8.094700 lr 0.00009352 rank 0
2023-07-06 07:55:41,586 DEBUG TRAIN Batch 21/108000 loss 4.924094 loss_att 4.013128 loss_ctc 7.049681 lr 0.00009350 rank 1
2023-07-06 07:55:41,588 DEBUG TRAIN Batch 21/108000 loss 5.573397 loss_att 4.686975 loss_ctc 7.641712 lr 0.00009350 rank 0
2023-07-06 07:59:56,431 DEBUG TRAIN Batch 21/109000 loss 5.689696 loss_att 4.716632 loss_ctc 7.960179 lr 0.00009349 rank 0
2023-07-06 07:59:56,455 DEBUG TRAIN Batch 21/109000 loss 3.789417 loss_att 3.177942 loss_ctc 5.216193 lr 0.00009348 rank 1
2023-07-06 08:04:07,552 DEBUG TRAIN Batch 21/110000 loss 2.220272 loss_att 1.793917 loss_ctc 3.215098 lr 0.00009347 rank 0
2023-07-06 08:04:07,555 DEBUG TRAIN Batch 21/110000 loss 5.583231 loss_att 4.717024 loss_ctc 7.604381 lr 0.00009347 rank 1
2023-07-06 08:08:20,260 DEBUG TRAIN Batch 21/111000 loss 5.562639 loss_att 4.473823 loss_ctc 8.103210 lr 0.00009345 rank 1
2023-07-06 08:08:20,262 DEBUG TRAIN Batch 21/111000 loss 2.752564 loss_att 2.178805 loss_ctc 4.091337 lr 0.00009345 rank 0
2023-07-06 08:12:33,229 DEBUG TRAIN Batch 21/112000 loss 5.165499 loss_att 4.086786 loss_ctc 7.682494 lr 0.00009344 rank 1
2023-07-06 08:12:33,230 DEBUG TRAIN Batch 21/112000 loss 2.637497 loss_att 2.219959 loss_ctc 3.611753 lr 0.00009344 rank 0
2023-07-06 08:16:37,712 DEBUG TRAIN Batch 21/113000 loss 3.035433 loss_att 2.518547 loss_ctc 4.241498 lr 0.00009342 rank 0
2023-07-06 08:16:37,717 DEBUG TRAIN Batch 21/113000 loss 4.413110 loss_att 3.543579 loss_ctc 6.442015 lr 0.00009342 rank 1
2023-07-06 08:20:40,585 DEBUG TRAIN Batch 21/114000 loss 2.363880 loss_att 1.906575 loss_ctc 3.430925 lr 0.00009340 rank 1
2023-07-06 08:20:40,587 DEBUG TRAIN Batch 21/114000 loss 2.566583 loss_att 2.220614 loss_ctc 3.373845 lr 0.00009341 rank 0
2023-07-06 08:24:53,728 DEBUG TRAIN Batch 21/115000 loss 8.831630 loss_att 7.330536 loss_ctc 12.334180 lr 0.00009339 rank 1
2023-07-06 08:24:53,733 DEBUG TRAIN Batch 21/115000 loss 2.824235 loss_att 2.563010 loss_ctc 3.433760 lr 0.00009339 rank 0
2023-07-06 08:29:01,780 DEBUG TRAIN Batch 21/116000 loss 2.220770 loss_att 1.730842 loss_ctc 3.363935 lr 0.00009337 rank 1
2023-07-06 08:29:01,780 DEBUG TRAIN Batch 21/116000 loss 5.430541 loss_att 4.247462 loss_ctc 8.191058 lr 0.00009337 rank 0
2023-07-06 08:33:13,262 DEBUG TRAIN Batch 21/117000 loss 4.745444 loss_att 3.761116 loss_ctc 7.042210 lr 0.00009335 rank 1
2023-07-06 08:33:13,263 DEBUG TRAIN Batch 21/117000 loss 8.521225 loss_att 7.235759 loss_ctc 11.520647 lr 0.00009336 rank 0
2023-07-06 08:37:27,784 DEBUG TRAIN Batch 21/118000 loss 2.860343 loss_att 2.449703 loss_ctc 3.818506 lr 0.00009334 rank 1
2023-07-06 08:37:27,786 DEBUG TRAIN Batch 21/118000 loss 1.791617 loss_att 1.476904 loss_ctc 2.525948 lr 0.00009334 rank 0
2023-07-06 08:41:32,107 DEBUG TRAIN Batch 21/119000 loss 6.811216 loss_att 6.081588 loss_ctc 8.513681 lr 0.00009332 rank 0
2023-07-06 08:41:32,109 DEBUG TRAIN Batch 21/119000 loss 6.738064 loss_att 5.688005 loss_ctc 9.188200 lr 0.00009332 rank 1
2023-07-06 08:45:40,510 DEBUG TRAIN Batch 21/120000 loss 5.423910 loss_att 4.683004 loss_ctc 7.152689 lr 0.00009331 rank 1
2023-07-06 08:45:40,513 DEBUG TRAIN Batch 21/120000 loss 5.022838 loss_att 4.309531 loss_ctc 6.687220 lr 0.00009331 rank 0
2023-07-06 08:50:01,514 DEBUG TRAIN Batch 21/121000 loss 2.623333 loss_att 2.042219 loss_ctc 3.979267 lr 0.00009329 rank 1
2023-07-06 08:50:01,517 DEBUG TRAIN Batch 21/121000 loss 5.526358 loss_att 4.454240 loss_ctc 8.027966 lr 0.00009329 rank 0
2023-07-06 08:50:28,680 WARNING NaN or Inf found in input tensor.
2023-07-06 08:54:04,112 DEBUG TRAIN Batch 21/122000 loss 4.080215 loss_att 3.378573 loss_ctc 5.717380 lr 0.00009327 rank 0
2023-07-06 08:54:04,113 DEBUG TRAIN Batch 21/122000 loss 4.490809 loss_att 3.762742 loss_ctc 6.189631 lr 0.00009327 rank 1
2023-07-06 08:58:10,034 DEBUG TRAIN Batch 21/123000 loss 3.958344 loss_att 3.300040 loss_ctc 5.494386 lr 0.00009326 rank 1
2023-07-06 08:58:10,046 DEBUG TRAIN Batch 21/123000 loss 6.687123 loss_att 5.652836 loss_ctc 9.100459 lr 0.00009326 rank 0
2023-07-06 09:02:18,246 DEBUG TRAIN Batch 21/124000 loss 3.503243 loss_att 3.038764 loss_ctc 4.587027 lr 0.00009324 rank 0
2023-07-06 09:02:18,248 DEBUG TRAIN Batch 21/124000 loss 2.552692 loss_att 2.220828 loss_ctc 3.327043 lr 0.00009324 rank 1
2023-07-06 09:06:32,845 DEBUG TRAIN Batch 21/125000 loss 5.601006 loss_att 4.432436 loss_ctc 8.327666 lr 0.00009322 rank 1
2023-07-06 09:06:32,846 DEBUG TRAIN Batch 21/125000 loss 6.135610 loss_att 5.144032 loss_ctc 8.449293 lr 0.00009323 rank 0
2023-07-06 09:10:34,711 DEBUG TRAIN Batch 21/126000 loss 5.473342 loss_att 4.436282 loss_ctc 7.893150 lr 0.00009321 rank 0
2023-07-06 09:10:34,712 DEBUG TRAIN Batch 21/126000 loss 1.783633 loss_att 1.535917 loss_ctc 2.361636 lr 0.00009321 rank 1
2023-07-06 09:14:42,150 DEBUG TRAIN Batch 21/127000 loss 2.409031 loss_att 2.095771 loss_ctc 3.139971 lr 0.00009319 rank 1
2023-07-06 09:14:42,152 DEBUG TRAIN Batch 21/127000 loss 3.458526 loss_att 3.001581 loss_ctc 4.524731 lr 0.00009319 rank 0
2023-07-06 09:18:43,341 DEBUG TRAIN Batch 21/128000 loss 3.547065 loss_att 2.805219 loss_ctc 5.278039 lr 0.00009318 rank 1
2023-07-06 09:18:43,343 DEBUG TRAIN Batch 21/128000 loss 4.513164 loss_att 3.982269 loss_ctc 5.751919 lr 0.00009318 rank 0
2023-07-06 09:19:12,250 WARNING NaN or Inf found in input tensor.
2023-07-06 09:22:50,205 DEBUG TRAIN Batch 21/129000 loss 3.887662 loss_att 3.020396 loss_ctc 5.911281 lr 0.00009316 rank 1
2023-07-06 09:22:50,206 DEBUG TRAIN Batch 21/129000 loss 7.780475 loss_att 6.256360 loss_ctc 11.336742 lr 0.00009316 rank 0
2023-07-06 09:27:01,809 DEBUG TRAIN Batch 21/130000 loss 3.334904 loss_att 2.827743 loss_ctc 4.518281 lr 0.00009314 rank 1
2023-07-06 09:27:01,815 DEBUG TRAIN Batch 21/130000 loss 4.896590 loss_att 4.129996 loss_ctc 6.685308 lr 0.00009315 rank 0
2023-07-06 09:31:06,749 DEBUG TRAIN Batch 21/131000 loss 5.719345 loss_att 5.034804 loss_ctc 7.316606 lr 0.00009313 rank 0
2023-07-06 09:31:26,076 DEBUG CV Batch 21/0 loss 3.671493 loss_att 3.218641 loss_ctc 4.728147 history loss 3.560235 rank 1
2023-07-06 09:31:26,115 DEBUG CV Batch 21/0 loss 3.500485 loss_att 3.041630 loss_ctc 4.571147 history loss 3.394410 rank 0
2023-07-06 09:32:40,233 DEBUG CV Batch 21/1000 loss 1.819051 loss_att 1.705053 loss_ctc 2.085046 history loss 5.129880 rank 0
2023-07-06 09:32:40,251 DEBUG CV Batch 21/1000 loss 1.934352 loss_att 1.800891 loss_ctc 2.245760 history loss 5.126218 rank 1
2023-07-06 09:33:45,079 DEBUG CV Batch 21/2000 loss 2.515625 loss_att 2.354415 loss_ctc 2.891781 history loss 5.414582 rank 1
2023-07-06 09:33:45,094 DEBUG CV Batch 21/2000 loss 2.644624 loss_att 2.444406 loss_ctc 3.111801 history loss 5.420209 rank 0
2023-07-06 09:34:17,078 INFO Epoch 21 CV info cv_loss 5.507164193498416
2023-07-06 09:34:17,081 INFO Checkpoint: save to checkpoint exp/u2++_efficonformer_shop/21.pt
2023-07-06 09:34:17,098 INFO Epoch 21 CV info cv_loss 5.500342865906914
2023-07-06 09:34:17,098 INFO Epoch 22 TRAIN info lr 9.312790798397288e-05
2023-07-06 09:34:17,100 INFO using accumulate grad, new batch size is 1 times larger than before
2023-07-06 09:34:17,332 INFO Epoch 22 TRAIN info lr 9.312808567416837e-05
2023-07-06 09:34:17,334 INFO using accumulate grad, new batch size is 1 times larger than before
2023-07-06 09:34:29,330 DEBUG TRAIN Batch 22/0 loss 9.475152 loss_att 7.638059 loss_ctc 13.761703 lr 0.00009313 rank 0
2023-07-06 09:34:29,385 DEBUG TRAIN Batch 22/0 loss 6.271044 loss_att 5.190169 loss_ctc 8.793083 lr 0.00009313 rank 1
2023-07-06 09:38:53,593 DEBUG TRAIN Batch 22/1000 loss 3.577475 loss_att 3.234612 loss_ctc 4.377489 lr 0.00009311 rank 1
2023-07-06 09:38:53,595 DEBUG TRAIN Batch 22/1000 loss 7.917165 loss_att 6.804665 loss_ctc 10.512998 lr 0.00009311 rank 0
2023-07-06 09:43:03,710 DEBUG TRAIN Batch 22/2000 loss 2.809398 loss_att 2.332321 loss_ctc 3.922577 lr 0.00009310 rank 1
2023-07-06 09:43:03,712 DEBUG TRAIN Batch 22/2000 loss 5.313488 loss_att 4.477454 loss_ctc 7.264235 lr 0.00009310 rank 0
2023-07-06 09:47:14,735 DEBUG TRAIN Batch 22/3000 loss 3.160513 loss_att 2.620294 loss_ctc 4.421025 lr 0.00009308 rank 1
2023-07-06 09:47:14,736 DEBUG TRAIN Batch 22/3000 loss 4.983922 loss_att 4.588909 loss_ctc 5.905618 lr 0.00009308 rank 0
2023-07-06 09:51:17,872 DEBUG TRAIN Batch 22/4000 loss 5.902550 loss_att 4.965381 loss_ctc 8.089277 lr 0.00009306 rank 0
2023-07-06 09:51:17,902 DEBUG TRAIN Batch 22/4000 loss 5.115418 loss_att 4.266396 loss_ctc 7.096470 lr 0.00009306 rank 1
2023-07-06 09:55:29,441 DEBUG TRAIN Batch 22/5000 loss 4.518969 loss_att 3.775999 loss_ctc 6.252564 lr 0.00009305 rank 1
2023-07-06 09:55:29,441 DEBUG TRAIN Batch 22/5000 loss 4.561963 loss_att 3.879833 loss_ctc 6.153599 lr 0.00009305 rank 0
2023-07-06 09:59:49,727 DEBUG TRAIN Batch 22/6000 loss 1.671193 loss_att 1.481375 loss_ctc 2.114100 lr 0.00009303 rank 1
2023-07-06 09:59:49,728 DEBUG TRAIN Batch 22/6000 loss 8.720482 loss_att 7.458995 loss_ctc 11.663952 lr 0.00009303 rank 0
2023-07-06 10:04:07,737 DEBUG TRAIN Batch 22/7000 loss 2.910903 loss_att 2.601227 loss_ctc 3.633483 lr 0.00009302 rank 0
2023-07-06 10:04:07,757 DEBUG TRAIN Batch 22/7000 loss 3.621362 loss_att 3.007862 loss_ctc 5.052863 lr 0.00009302 rank 1
2023-07-06 10:06:50,019 WARNING NaN or Inf found in input tensor.
2023-07-06 10:08:16,116 DEBUG TRAIN Batch 22/8000 loss 4.553403 loss_att 3.856019 loss_ctc 6.180634 lr 0.00009300 rank 1
2023-07-06 10:08:16,119 DEBUG TRAIN Batch 22/8000 loss 6.864382 loss_att 5.782465 loss_ctc 9.388857 lr 0.00009300 rank 0
2023-07-06 10:12:29,783 DEBUG TRAIN Batch 22/9000 loss 3.320096 loss_att 2.923174 loss_ctc 4.246249 lr 0.00009298 rank 1
2023-07-06 10:12:29,812 DEBUG TRAIN Batch 22/9000 loss 6.218045 loss_att 5.300231 loss_ctc 8.359612 lr 0.00009298 rank 0
2023-07-06 10:16:43,336 DEBUG TRAIN Batch 22/10000 loss 3.635230 loss_att 3.123440 loss_ctc 4.829407 lr 0.00009297 rank 0
2023-07-06 10:16:43,361 DEBUG TRAIN Batch 22/10000 loss 3.542800 loss_att 3.121959 loss_ctc 4.524763 lr 0.00009297 rank 1
2023-07-06 10:20:45,032 DEBUG TRAIN Batch 22/11000 loss 4.155707 loss_att 3.382951 loss_ctc 5.958804 lr 0.00009295 rank 1
2023-07-06 10:20:45,036 DEBUG TRAIN Batch 22/11000 loss 5.331535 loss_att 4.051831 loss_ctc 8.317511 lr 0.00009295 rank 0
2023-07-06 10:24:51,767 DEBUG TRAIN Batch 22/12000 loss 2.513472 loss_att 2.133261 loss_ctc 3.400632 lr 0.00009293 rank 1
2023-07-06 10:24:51,769 DEBUG TRAIN Batch 22/12000 loss 2.543992 loss_att 2.382159 loss_ctc 2.921602 lr 0.00009293 rank 0
2023-07-06 10:28:54,646 DEBUG TRAIN Batch 22/13000 loss 5.600762 loss_att 4.588363 loss_ctc 7.963029 lr 0.00009292 rank 1
2023-07-06 10:28:54,647 DEBUG TRAIN Batch 22/13000 loss 1.316208 loss_att 1.150409 loss_ctc 1.703073 lr 0.00009292 rank 0
2023-07-06 10:33:06,370 DEBUG TRAIN Batch 22/14000 loss 4.358975 loss_att 3.677336 loss_ctc 5.949466 lr 0.00009290 rank 1
2023-07-06 10:33:06,374 DEBUG TRAIN Batch 22/14000 loss 6.048322 loss_att 4.914592 loss_ctc 8.693691 lr 0.00009290 rank 0
2023-07-06 10:37:06,999 DEBUG TRAIN Batch 22/15000 loss 4.819028 loss_att 3.993965 loss_ctc 6.744176 lr 0.00009289 rank 0
2023-07-06 10:37:07,006 DEBUG TRAIN Batch 22/15000 loss 5.135914 loss_att 4.246826 loss_ctc 7.210452 lr 0.00009289 rank 1
2023-07-06 10:41:15,329 DEBUG TRAIN Batch 22/16000 loss 2.522131 loss_att 2.185067 loss_ctc 3.308613 lr 0.00009287 rank 0
2023-07-06 10:41:15,354 DEBUG TRAIN Batch 22/16000 loss 4.559290 loss_att 3.911601 loss_ctc 6.070563 lr 0.00009287 rank 1
2023-07-06 10:45:24,768 DEBUG TRAIN Batch 22/17000 loss 3.080112 loss_att 2.749640 loss_ctc 3.851213 lr 0.00009285 rank 1
2023-07-06 10:45:24,806 DEBUG TRAIN Batch 22/17000 loss 5.985538 loss_att 4.962017 loss_ctc 8.373753 lr 0.00009285 rank 0
2023-07-06 10:46:34,934 WARNING NaN or Inf found in input tensor.
2023-07-06 10:49:33,223 DEBUG TRAIN Batch 22/18000 loss 3.456576 loss_att 2.874109 loss_ctc 4.815665 lr 0.00009284 rank 1
2023-07-06 10:49:33,254 DEBUG TRAIN Batch 22/18000 loss 5.984648 loss_att 5.315328 loss_ctc 7.546395 lr 0.00009284 rank 0
2023-07-06 10:53:44,312 DEBUG TRAIN Batch 22/19000 loss 2.076604 loss_att 1.866452 loss_ctc 2.566957 lr 0.00009282 rank 1
2023-07-06 10:53:44,312 DEBUG TRAIN Batch 22/19000 loss 1.779774 loss_att 1.570705 loss_ctc 2.267603 lr 0.00009282 rank 0
2023-07-06 10:53:54,037 WARNING NaN or Inf found in input tensor.
2023-07-06 10:57:44,810 DEBUG TRAIN Batch 22/20000 loss 2.785491 loss_att 2.599598 loss_ctc 3.219241 lr 0.00009281 rank 0
2023-07-06 10:57:44,810 DEBUG TRAIN Batch 22/20000 loss 1.916570 loss_att 1.685910 loss_ctc 2.454775 lr 0.00009281 rank 1
2023-07-06 11:01:52,192 DEBUG TRAIN Batch 22/21000 loss 8.551622 loss_att 7.268942 loss_ctc 11.544542 lr 0.00009279 rank 1
2023-07-06 11:01:52,192 DEBUG TRAIN Batch 22/21000 loss 4.107963 loss_att 3.318407 loss_ctc 5.950259 lr 0.00009279 rank 0
2023-07-06 11:06:00,157 DEBUG TRAIN Batch 22/22000 loss 8.563411 loss_att 7.241217 loss_ctc 11.648531 lr 0.00009277 rank 1
2023-07-06 11:06:00,159 DEBUG TRAIN Batch 22/22000 loss 6.721056 loss_att 5.851925 loss_ctc 8.749028 lr 0.00009277 rank 0
2023-07-06 11:10:12,892 DEBUG TRAIN Batch 22/23000 loss 2.824095 loss_att 2.532462 loss_ctc 3.504571 lr 0.00009276 rank 1
2023-07-06 11:10:12,895 DEBUG TRAIN Batch 22/23000 loss 6.883400 loss_att 5.883931 loss_ctc 9.215496 lr 0.00009276 rank 0
2023-07-06 11:14:18,959 DEBUG TRAIN Batch 22/24000 loss 3.149480 loss_att 2.536697 loss_ctc 4.579306 lr 0.00009274 rank 1
2023-07-06 11:14:18,962 DEBUG TRAIN Batch 22/24000 loss 3.264495 loss_att 3.039065 loss_ctc 3.790497 lr 0.00009274 rank 0
2023-07-06 11:18:34,160 DEBUG TRAIN Batch 22/25000 loss 3.516408 loss_att 2.860618 loss_ctc 5.046583 lr 0.00009273 rank 1
2023-07-06 11:18:34,167 DEBUG TRAIN Batch 22/25000 loss 2.418685 loss_att 2.198391 loss_ctc 2.932703 lr 0.00009273 rank 0
2023-07-06 11:22:41,364 DEBUG TRAIN Batch 22/26000 loss 3.784490 loss_att 3.119822 loss_ctc 5.335381 lr 0.00009271 rank 1
2023-07-06 11:22:41,394 DEBUG TRAIN Batch 22/26000 loss 3.549399 loss_att 2.976012 loss_ctc 4.887302 lr 0.00009271 rank 0
2023-07-06 11:26:51,002 DEBUG TRAIN Batch 22/27000 loss 3.489787 loss_att 2.958178 loss_ctc 4.730208 lr 0.00009269 rank 0
2023-07-06 11:26:51,027 DEBUG TRAIN Batch 22/27000 loss 4.455992 loss_att 3.756761 loss_ctc 6.087532 lr 0.00009269 rank 1
2023-07-06 11:28:55,579 WARNING NaN or Inf found in input tensor.
2023-07-06 11:31:14,840 DEBUG TRAIN Batch 22/28000 loss 4.796553 loss_att 4.163041 loss_ctc 6.274747 lr 0.00009268 rank 0
2023-07-06 11:31:14,862 DEBUG TRAIN Batch 22/28000 loss 5.590244 loss_att 4.750604 loss_ctc 7.549405 lr 0.00009268 rank 1
2023-07-06 11:35:33,002 DEBUG TRAIN Batch 22/29000 loss 3.820283 loss_att 3.228755 loss_ctc 5.200517 lr 0.00009266 rank 0
2023-07-06 11:35:33,006 DEBUG TRAIN Batch 22/29000 loss 5.954581 loss_att 4.677671 loss_ctc 8.934036 lr 0.00009266 rank 1
2023-07-06 11:39:51,923 DEBUG TRAIN Batch 22/30000 loss 1.968304 loss_att 1.645986 loss_ctc 2.720380 lr 0.00009265 rank 1
2023-07-06 11:39:51,928 DEBUG TRAIN Batch 22/30000 loss 1.706392 loss_att 1.449362 loss_ctc 2.306129 lr 0.00009265 rank 0
2023-07-06 11:44:04,687 DEBUG TRAIN Batch 22/31000 loss 8.431679 loss_att 7.209462 loss_ctc 11.283518 lr 0.00009263 rank 1
2023-07-06 11:44:04,690 DEBUG TRAIN Batch 22/31000 loss 5.157695 loss_att 4.080265 loss_ctc 7.671699 lr 0.00009263 rank 0
2023-07-06 11:48:09,187 DEBUG TRAIN Batch 22/32000 loss 4.681931 loss_att 3.937454 loss_ctc 6.419045 lr 0.00009262 rank 0
2023-07-06 11:48:09,187 DEBUG TRAIN Batch 22/32000 loss 5.592994 loss_att 4.386949 loss_ctc 8.407099 lr 0.00009262 rank 1
2023-07-06 11:52:25,330 DEBUG TRAIN Batch 22/33000 loss 3.813298 loss_att 3.201815 loss_ctc 5.240092 lr 0.00009260 rank 1
2023-07-06 11:52:25,332 DEBUG TRAIN Batch 22/33000 loss 3.353897 loss_att 2.937456 loss_ctc 4.325593 lr 0.00009260 rank 0
2023-07-06 11:56:39,271 DEBUG TRAIN Batch 22/34000 loss 5.473385 loss_att 4.525765 loss_ctc 7.684497 lr 0.00009258 rank 1
2023-07-06 11:56:39,273 DEBUG TRAIN Batch 22/34000 loss 3.774817 loss_att 3.004714 loss_ctc 5.571722 lr 0.00009258 rank 0
2023-07-06 12:00:56,458 DEBUG TRAIN Batch 22/35000 loss 3.247173 loss_att 2.732170 loss_ctc 4.448848 lr 0.00009257 rank 1
2023-07-06 12:00:56,487 DEBUG TRAIN Batch 22/35000 loss 6.736091 loss_att 5.813115 loss_ctc 8.889700 lr 0.00009257 rank 0
2023-07-06 12:05:11,614 DEBUG TRAIN Batch 22/36000 loss 5.446014 loss_att 4.359777 loss_ctc 7.980567 lr 0.00009255 rank 1
2023-07-06 12:05:11,622 DEBUG TRAIN Batch 22/36000 loss 4.523958 loss_att 3.844404 loss_ctc 6.109583 lr 0.00009255 rank 0
2023-07-06 12:09:20,655 DEBUG TRAIN Batch 22/37000 loss 6.721089 loss_att 5.974741 loss_ctc 8.462567 lr 0.00009254 rank 1
2023-07-06 12:09:20,656 DEBUG TRAIN Batch 22/37000 loss 4.422328 loss_att 3.356769 loss_ctc 6.908631 lr 0.00009254 rank 0
2023-07-06 12:13:38,748 DEBUG TRAIN Batch 22/38000 loss 4.379912 loss_att 3.827329 loss_ctc 5.669273 lr 0.00009252 rank 1
2023-07-06 12:13:38,779 DEBUG TRAIN Batch 22/38000 loss 3.231000 loss_att 2.843585 loss_ctc 4.134970 lr 0.00009252 rank 0
2023-07-06 12:17:53,596 DEBUG TRAIN Batch 22/39000 loss 4.571571 loss_att 3.720915 loss_ctc 6.556437 lr 0.00009250 rank 0
2023-07-06 12:17:53,596 DEBUG TRAIN Batch 22/39000 loss 2.132600 loss_att 1.830812 loss_ctc 2.836774 lr 0.00009250 rank 1
2023-07-06 12:22:16,099 DEBUG TRAIN Batch 22/40000 loss 2.205812 loss_att 1.763156 loss_ctc 3.238677 lr 0.00009249 rank 1
2023-07-06 12:22:16,101 DEBUG TRAIN Batch 22/40000 loss 5.037985 loss_att 4.044805 loss_ctc 7.355406 lr 0.00009249 rank 0
2023-07-06 12:26:35,556 DEBUG TRAIN Batch 22/41000 loss 5.616280 loss_att 4.621264 loss_ctc 7.937984 lr 0.00009247 rank 0
2023-07-06 12:26:35,556 DEBUG TRAIN Batch 22/41000 loss 2.972201 loss_att 2.534209 loss_ctc 3.994181 lr 0.00009247 rank 1
2023-07-06 12:30:52,146 DEBUG TRAIN Batch 22/42000 loss 3.891124 loss_att 3.103322 loss_ctc 5.729331 lr 0.00009246 rank 1
2023-07-06 12:30:52,148 DEBUG TRAIN Batch 22/42000 loss 6.883904 loss_att 5.627391 loss_ctc 9.815769 lr 0.00009246 rank 0
2023-07-06 12:35:06,958 DEBUG TRAIN Batch 22/43000 loss 7.647262 loss_att 6.614895 loss_ctc 10.056116 lr 0.00009244 rank 1
2023-07-06 12:35:06,961 DEBUG TRAIN Batch 22/43000 loss 6.494004 loss_att 5.142135 loss_ctc 9.648365 lr 0.00009244 rank 0
2023-07-06 12:39:15,375 DEBUG TRAIN Batch 22/44000 loss 5.902075 loss_att 4.848857 loss_ctc 8.359585 lr 0.00009243 rank 1
2023-07-06 12:39:15,381 DEBUG TRAIN Batch 22/44000 loss 5.539476 loss_att 4.660049 loss_ctc 7.591472 lr 0.00009243 rank 0
2023-07-06 12:43:21,377 DEBUG TRAIN Batch 22/45000 loss 1.366157 loss_att 1.226574 loss_ctc 1.691851 lr 0.00009241 rank 1
2023-07-06 12:43:21,378 DEBUG TRAIN Batch 22/45000 loss 4.765386 loss_att 3.970180 loss_ctc 6.620866 lr 0.00009241 rank 0
2023-07-06 12:47:27,534 DEBUG TRAIN Batch 22/46000 loss 4.805575 loss_att 4.147903 loss_ctc 6.340143 lr 0.00009239 rank 1
2023-07-06 12:47:27,541 DEBUG TRAIN Batch 22/46000 loss 5.866831 loss_att 5.043241 loss_ctc 7.788541 lr 0.00009239 rank 0
2023-07-06 12:51:33,809 DEBUG TRAIN Batch 22/47000 loss 2.179729 loss_att 1.853956 loss_ctc 2.939868 lr 0.00009238 rank 1
2023-07-06 12:51:33,813 DEBUG TRAIN Batch 22/47000 loss 4.634946 loss_att 3.616262 loss_ctc 7.011873 lr 0.00009238 rank 0
2023-07-06 12:55:41,140 DEBUG TRAIN Batch 22/48000 loss 4.449184 loss_att 3.440480 loss_ctc 6.802825 lr 0.00009236 rank 0
2023-07-06 12:55:41,165 DEBUG TRAIN Batch 22/48000 loss 4.455440 loss_att 3.875506 loss_ctc 5.808618 lr 0.00009236 rank 1
2023-07-06 12:59:55,361 DEBUG TRAIN Batch 22/49000 loss 5.077661 loss_att 4.326614 loss_ctc 6.830102 lr 0.00009235 rank 0
2023-07-06 12:59:55,363 DEBUG TRAIN Batch 22/49000 loss 3.572996 loss_att 2.824580 loss_ctc 5.319301 lr 0.00009235 rank 1
2023-07-06 13:04:04,993 DEBUG TRAIN Batch 22/50000 loss 4.591138 loss_att 3.825414 loss_ctc 6.377828 lr 0.00009233 rank 1
2023-07-06 13:04:04,999 DEBUG TRAIN Batch 22/50000 loss 7.650589 loss_att 6.587306 loss_ctc 10.131582 lr 0.00009233 rank 0
2023-07-06 13:08:11,854 DEBUG TRAIN Batch 22/51000 loss 2.803599 loss_att 2.273142 loss_ctc 4.041331 lr 0.00009231 rank 1
2023-07-06 13:08:11,857 DEBUG TRAIN Batch 22/51000 loss 4.794803 loss_att 3.902230 loss_ctc 6.877471 lr 0.00009232 rank 0
2023-07-06 13:12:27,711 DEBUG TRAIN Batch 22/52000 loss 4.130168 loss_att 3.422869 loss_ctc 5.780534 lr 0.00009230 rank 0
2023-07-06 13:12:27,731 DEBUG TRAIN Batch 22/52000 loss 1.576198 loss_att 1.375565 loss_ctc 2.044341 lr 0.00009230 rank 1
2023-07-06 13:16:13,298 WARNING NaN or Inf found in input tensor.
2023-07-06 13:16:44,635 DEBUG TRAIN Batch 22/53000 loss 3.082188 loss_att 2.420652 loss_ctc 4.625772 lr 0.00009228 rank 1
2023-07-06 13:16:44,639 DEBUG TRAIN Batch 22/53000 loss 5.908051 loss_att 4.948901 loss_ctc 8.146069 lr 0.00009228 rank 0
2023-07-06 13:20:50,126 DEBUG TRAIN Batch 22/54000 loss 6.871432 loss_att 5.871257 loss_ctc 9.205175 lr 0.00009227 rank 1
2023-07-06 13:20:50,127 DEBUG TRAIN Batch 22/54000 loss 5.653519 loss_att 4.771131 loss_ctc 7.712425 lr 0.00009227 rank 0
wav: Premature EOF on .wav input file
2023-07-06 13:25:05,699 DEBUG TRAIN Batch 22/55000 loss 4.585919 loss_att 4.021787 loss_ctc 5.902229 lr 0.00009225 rank 1
2023-07-06 13:25:05,723 DEBUG TRAIN Batch 22/55000 loss 5.716120 loss_att 4.304062 loss_ctc 9.010921 lr 0.00009225 rank 0
2023-07-06 13:29:18,797 DEBUG TRAIN Batch 22/56000 loss 3.729799 loss_att 2.997276 loss_ctc 5.439020 lr 0.00009224 rank 0
2023-07-06 13:29:18,823 DEBUG TRAIN Batch 22/56000 loss 4.832637 loss_att 4.065464 loss_ctc 6.622708 lr 0.00009224 rank 1
2023-07-06 13:29:23,368 WARNING NaN or Inf found in input tensor.
2023-07-06 13:33:37,408 DEBUG TRAIN Batch 22/57000 loss 5.638543 loss_att 4.722363 loss_ctc 7.776298 lr 0.00009222 rank 0
2023-07-06 13:33:37,412 DEBUG TRAIN Batch 22/57000 loss 2.794919 loss_att 2.422510 loss_ctc 3.663875 lr 0.00009222 rank 1
2023-07-06 13:38:01,748 DEBUG TRAIN Batch 22/58000 loss 3.226184 loss_att 2.502113 loss_ctc 4.915685 lr 0.00009220 rank 1
2023-07-06 13:38:01,750 DEBUG TRAIN Batch 22/58000 loss 3.634109 loss_att 2.985034 loss_ctc 5.148618 lr 0.00009221 rank 0
2023-07-06 13:42:07,790 DEBUG TRAIN Batch 22/59000 loss 4.046184 loss_att 3.403335 loss_ctc 5.546165 lr 0.00009219 rank 0
2023-07-06 13:42:07,793 DEBUG TRAIN Batch 22/59000 loss 4.238891 loss_att 3.631308 loss_ctc 5.656584 lr 0.00009219 rank 1
2023-07-06 13:46:21,704 DEBUG TRAIN Batch 22/60000 loss 1.988631 loss_att 1.671747 loss_ctc 2.728028 lr 0.00009217 rank 1
2023-07-06 13:46:21,709 DEBUG TRAIN Batch 22/60000 loss 5.301411 loss_att 3.929282 loss_ctc 8.503044 lr 0.00009217 rank 0
2023-07-06 13:50:31,186 DEBUG TRAIN Batch 22/61000 loss 8.141562 loss_att 7.147025 loss_ctc 10.462149 lr 0.00009216 rank 1
2023-07-06 13:50:31,186 DEBUG TRAIN Batch 22/61000 loss 6.436068 loss_att 5.691197 loss_ctc 8.174100 lr 0.00009216 rank 0
2023-07-06 13:54:41,672 DEBUG TRAIN Batch 22/62000 loss 6.334331 loss_att 5.509443 loss_ctc 8.259068 lr 0.00009214 rank 1
2023-07-06 13:54:41,676 DEBUG TRAIN Batch 22/62000 loss 1.382185 loss_att 1.295442 loss_ctc 1.584583 lr 0.00009214 rank 0
2023-07-06 13:58:57,346 DEBUG TRAIN Batch 22/63000 loss 4.181996 loss_att 3.640898 loss_ctc 5.444561 lr 0.00009213 rank 1
2023-07-06 13:58:57,348 DEBUG TRAIN Batch 22/63000 loss 5.942790 loss_att 4.891045 loss_ctc 8.396861 lr 0.00009213 rank 0
2023-07-06 14:03:05,916 DEBUG TRAIN Batch 22/64000 loss 6.761470 loss_att 5.698538 loss_ctc 9.241646 lr 0.00009211 rank 1
2023-07-06 14:03:05,917 DEBUG TRAIN Batch 22/64000 loss 5.379012 loss_att 4.585407 loss_ctc 7.230758 lr 0.00009211 rank 0
2023-07-06 14:07:07,602 DEBUG TRAIN Batch 22/65000 loss 5.464413 loss_att 4.572439 loss_ctc 7.545686 lr 0.00009210 rank 1
2023-07-06 14:07:07,603 DEBUG TRAIN Batch 22/65000 loss 3.913684 loss_att 3.385918 loss_ctc 5.145140 lr 0.00009210 rank 0
2023-07-06 14:11:16,181 DEBUG TRAIN Batch 22/66000 loss 1.857252 loss_att 1.591041 loss_ctc 2.478413 lr 0.00009208 rank 0
2023-07-06 14:11:16,181 DEBUG TRAIN Batch 22/66000 loss 3.491541 loss_att 3.068439 loss_ctc 4.478781 lr 0.00009208 rank 1
2023-07-06 14:15:21,785 DEBUG TRAIN Batch 22/67000 loss 3.044906 loss_att 2.455209 loss_ctc 4.420868 lr 0.00009206 rank 0
2023-07-06 14:15:21,808 DEBUG TRAIN Batch 22/67000 loss 1.806772 loss_att 1.669161 loss_ctc 2.127863 lr 0.00009206 rank 1
run_u2_efficonformer_v2_shop.sh: line 196: 102270 Terminated              python wenet/bin/train.py --gpu $gpu_id --config $train_config --data_type $data_type --symbol_table $dict --train_data $datadir/$train_set/data.list --cv_data data/dev_cuishou/data.list ${checkpoint:+--checkpoint $checkpoint} --model_dir $dir --ddp.init_method $init_method --ddp.world_size $world_size --ddp.rank $rank --ddp.dist_backend $dist_backend --num_workers ${num_workers} --prefetch ${prefetch} $cmvn_opts --pin_memory
run_u2_efficonformer_v2_shop.sh: line 196: 102271 Terminated              python wenet/bin/train.py --gpu $gpu_id --config $train_config --data_type $data_type --symbol_table $dict --train_data $datadir/$train_set/data.list --cv_data data/dev_cuishou/data.list ${checkpoint:+--checkpoint $checkpoint} --model_dir $dir --ddp.init_method $init_method --ddp.world_size $world_size --ddp.rank $rank --ddp.dist_backend $dist_backend --num_workers ${num_workers} --prefetch ${prefetch} $cmvn_opts --pin_memory
