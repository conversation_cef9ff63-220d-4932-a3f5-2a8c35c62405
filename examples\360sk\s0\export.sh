#!/bin/bash

bash path.sh || exit 1;

export CUDA_VISIBLE_DEVICES="0"

# model_dir=/data/cjm/asr_tool/wenet-main/examples/360sk/s0/exp/cuishou_v3.2_fintune_v2
model_dir=/data/oceanus_ctr/j-chenjinming-jk/gpu01/workdir/chenjinming/asr-qifree/examples/360sk/s0/exp/qifree_u2++_efficonformer_v1_group2_stream
# onnx_model_dir=/data/cjm/asr_tool/wenet-main/examples/360sk/s0/exp/cuishou_v4.2_gpu
# onnx_model_dir=/data/cjm/asr_tool/wenet-main/examples/360sk/s0/exp/dianxiao_v4_gpu
onnx_model_dir=/data/oceanus_ctr/j-chenjinming-jk/gpu01/workdir/chenjinming/01_WENET/04_model/qifree_v2.2_gpu

mkdir -p $onnx_model_dir
python wenet/bin/export_onnx_gpu.py --config=$model_dir/train.yaml --checkpoint=$model_dir/avg_5.pt --cmvn_file=$model_dir/global_cmvn --ctc_weight=0.3 --reverse_weight=0.5 --output_onnx_dir=$onnx_model_dir --fp16
cp $model_dir/lang_char.txt $model_dir/train.yaml $onnx_model_dir/

# onnx_model_dir=${onnx_model_dir}_stream
# mkdir -p $onnx_model_dir
# python wenet/bin/export_onnx_gpu.py --config=$model_dir/train.yaml --checkpoint=$model_dir/avg_5.pt --cmvn_file=$model_dir/global_cmvn  --ctc_weight=0.3 --reverse_weight=0.5 --output_onnx_dir=$onnx_model_dir --fp16 --streaming
# cp $model_dir/lang_char.txt $model_dir/train.yaml $onnx_model_dir/
