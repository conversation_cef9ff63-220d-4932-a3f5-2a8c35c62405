run_u2_efficonformer_v2_fintune_v3.sh: init method is file:///data/cjm/asr_tool/wenet-main/examples/360sk/s0/exp/u2++_efficonformer_cuishou_finetune_v5/ddp_init
total gpus is: 4
using torch ddp
/home/<USER>/miniconda3/envs/wenet/lib/python3.8/site-packages/torch/_jit_internal.py:726: FutureWarning: ignore(True) has been deprecated. TorchScript will now drop the function call on compilation. Use torch.jit.unused now. {}
  warnings.warn(
2023-07-19 17:18:38,341 INFO training on multiple gpus, this gpu 1
/home/<USER>/miniconda3/envs/wenet/lib/python3.8/site-packages/torch/_jit_internal.py:726: FutureWarning: ignore(True) has been deprecated. TorchScript will now drop the function call on compilation. Use torch.jit.unused now. {}
  warnings.warn(
2023-07-19 17:18:38,342 INFO training on multiple gpus, this gpu 2
/home/<USER>/miniconda3/envs/wenet/lib/python3.8/site-packages/torch/_jit_internal.py:726: FutureWarning: ignore(True) has been deprecated. TorchScript will now drop the function call on compilation. Use torch.jit.unused now. {}
  warnings.warn(
2023-07-19 17:18:38,342 INFO training on multiple gpus, this gpu 0
/home/<USER>/miniconda3/envs/wenet/lib/python3.8/site-packages/torch/_jit_internal.py:726: FutureWarning: ignore(True) has been deprecated. TorchScript will now drop the function call on compilation. Use torch.jit.unused now. {}
  warnings.warn(
2023-07-19 17:18:38,342 INFO training on multiple gpus, this gpu 3
2023-07-19 17:18:44,110 INFO Added key: store_based_barrier_key:1 to store for rank: 3
2023-07-19 17:18:44,192 INFO Added key: store_based_barrier_key:1 to store for rank: 2
2023-07-19 17:18:44,273 INFO Added key: store_based_barrier_key:1 to store for rank: 0
2023-07-19 17:18:44,350 INFO Added key: store_based_barrier_key:1 to store for rank: 1
2023-07-19 17:18:44,350 INFO Rank 1: Completed store-based barrier for key:store_based_barrier_key:1 with 4 nodes.
2023-07-19 17:18:44,354 INFO Rank 0: Completed store-based barrier for key:store_based_barrier_key:1 with 4 nodes.
2023-07-19 17:18:44,355 INFO input_layer = conv2d2, subsampling_class = <class 'wenet.efficient_conformer.subsampling.Conv2dSubsampling2'>
2023-07-19 17:18:44,355 INFO Rank 3: Completed store-based barrier for key:store_based_barrier_key:1 with 4 nodes.
2023-07-19 17:18:44,356 INFO Rank 2: Completed store-based barrier for key:store_based_barrier_key:1 with 4 nodes.
2023-07-19 17:18:44,360 INFO input_layer = conv2d2, subsampling_class = <class 'wenet.efficient_conformer.subsampling.Conv2dSubsampling2'>
2023-07-19 17:18:44,360 INFO input_layer = conv2d2, subsampling_class = <class 'wenet.efficient_conformer.subsampling.Conv2dSubsampling2'>
2023-07-19 17:18:44,367 INFO input_layer = conv2d2, subsampling_class = <class 'wenet.efficient_conformer.subsampling.Conv2dSubsampling2'>
2023-07-19 17:18:44,465 INFO stride_layer_idx= [3, 7], stride = [2, 2], cnn_module_kernel = [15, 15, 15], group_layer_idx = [3, 7], grouped_size = 3
2023-07-19 17:18:44,475 INFO stride_layer_idx= [3, 7], stride = [2, 2], cnn_module_kernel = [15, 15, 15], group_layer_idx = [3, 7], grouped_size = 3
2023-07-19 17:18:44,476 INFO stride_layer_idx= [3, 7], stride = [2, 2], cnn_module_kernel = [15, 15, 15], group_layer_idx = [3, 7], grouped_size = 3
2023-07-19 17:18:44,527 INFO stride_layer_idx= [3, 7], stride = [2, 2], cnn_module_kernel = [15, 15, 15], group_layer_idx = [3, 7], grouped_size = 3
2023-07-19 17:18:44,802 INFO Checkpoint: loading from checkpoint exp/u2++_efficonformer_cuishou_finetune_v5/8.pt for GPU
2023-07-19 17:18:44,815 INFO Checkpoint: loading from checkpoint exp/u2++_efficonformer_cuishou_finetune_v5/8.pt for GPU
2023-07-19 17:18:44,817 INFO Checkpoint: loading from checkpoint exp/u2++_efficonformer_cuishou_finetune_v5/8.pt for GPU
Failed to import k2 and icefall.         Notice that they are necessary for         hlg_onebest/hlg_rescore decoding and LF-MMI training
ASRModel(
  (encoder): EfficientConformerEncoder(
    (global_cmvn): GlobalCMVN()
    (embed): Conv2dSubsampling2(
      (conv): Sequential(
        (0): Conv2d(1, 256, kernel_size=(3, 3), stride=(2, 2))
        (1): ReLU()
      )
      (out): Sequential(
        (0): Linear(in_features=4864, out_features=256, bias=True)
      )
      (pos_enc): RelPositionalEncoding(
        (dropout): Dropout(p=0.1, inplace=False)
      )
    )
    (after_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    (encoders): ModuleList(
      (0): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (1): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (2): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (3): StrideConformerEncoderLayer(
        (self_attn): GroupedRelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(2,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (pointwise_conv_layer): AvgPool1d(kernel_size=(2,), stride=(2,), padding=(0,))
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (concat_linear): Linear(in_features=512, out_features=256, bias=True)
      )
      (4): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (5): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (6): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (7): StrideConformerEncoderLayer(
        (self_attn): GroupedRelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(2,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (pointwise_conv_layer): AvgPool1d(kernel_size=(2,), stride=(2,), padding=(0,))
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (concat_linear): Linear(in_features=512, out_features=256, bias=True)
      )
      (8): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (9): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (10): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (11): ConformerEncoderLayer(
        (self_attn): RelPositionMultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (linear_pos): Linear(in_features=256, out_features=256, bias=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (feed_forward_macaron): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): SiLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (conv_module): ConvolutionModule(
          (pointwise_conv1): Conv1d(256, 512, kernel_size=(1,), stride=(1,))
          (depthwise_conv): Conv1d(256, 256, kernel_size=(15,), stride=(1,), groups=256)
          (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (pointwise_conv2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
          (activation): SiLU()
        )
        (norm_ff): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_mha): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_ff_macaron): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_conv): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm_final): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
    )
  )
  (decoder): TransformerDecoder(
    (embed): Sequential(
      (0): Embedding(3828, 256)
      (1): PositionalEncoding(
        (dropout): Dropout(p=0.1, inplace=False)
      )
    )
    (after_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    (output_layer): Linear(in_features=256, out_features=3828, bias=True)
    (decoders): ModuleList(
      (0): DecoderLayer(
        (self_attn): MultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.0, inplace=False)
        )
        (src_attn): MultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.0, inplace=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): ReLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (1): DecoderLayer(
        (self_attn): MultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.0, inplace=False)
        )
        (src_attn): MultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.0, inplace=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): ReLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (2): DecoderLayer(
        (self_attn): MultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.0, inplace=False)
        )
        (src_attn): MultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.0, inplace=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): ReLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (3): DecoderLayer(
        (self_attn): MultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.0, inplace=False)
        )
        (src_attn): MultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.0, inplace=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): ReLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (4): DecoderLayer(
        (self_attn): MultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.0, inplace=False)
        )
        (src_attn): MultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.0, inplace=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): ReLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
      (5): DecoderLayer(
        (self_attn): MultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.0, inplace=False)
        )
        (src_attn): MultiHeadedAttention(
          (linear_q): Linear(in_features=256, out_features=256, bias=True)
          (linear_k): Linear(in_features=256, out_features=256, bias=True)
          (linear_v): Linear(in_features=256, out_features=256, bias=True)
          (linear_out): Linear(in_features=256, out_features=256, bias=True)
          (dropout): Dropout(p=0.0, inplace=False)
        )
        (feed_forward): PositionwiseFeedForward(
          (w_1): Linear(in_features=256, out_features=2048, bias=True)
          (activation): ReLU()
          (dropout): Dropout(p=0.1, inplace=False)
          (w_2): Linear(in_features=2048, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)
      )
    )
  )
  (ctc): CTC(
    (ctc_lo): Linear(in_features=256, out_features=3828, bias=True)
    (ctc_loss): CTCLoss()
  )
  (criterion_att): LabelSmoothingLoss(
    (criterion): KLDivLoss()
  )
)2023-07-19 17:18:46,178 INFO Checkpoint: loading from checkpoint exp/u2++_efficonformer_cuishou_finetune_v5/8.pt for GPU
hpcamp18:126080:126080 [0] NCCL INFO Bootstrap : Using eth4:10.228.64.149<0>
hpcamp18:126080:126080 [0] NCCL INFO NET/Plugin : No plugin found (libnccl-net.so), using internal implementation
hpcamp18:126080:126080 [0] NCCL INFO cudaDriverVersion 11060
NCCL version 2.14.3+cuda11.7
2023-07-19 17:18:48,662 INFO Epoch 9 TRAIN info lr 4e-08
2023-07-19 17:18:48,662 INFO Epoch 9 TRAIN info lr 4e-08
2023-07-19 17:18:48,663 INFO Epoch 9 TRAIN info lr 4e-08
2023-07-19 17:18:48,664 INFO using accumulate grad, new batch size is 1 times larger than before
2023-07-19 17:18:48,664 INFO using accumulate grad, new batch size is 1 times larger than before
2023-07-19 17:18:48,665 INFO using accumulate grad, new batch size is 1 times larger than before

the number of model params: 45,560,040
Failed to import k2 and icefall.         Notice that they are necessary for         hlg_onebest/hlg_rescore decoding and LF-MMI training
Failed to import k2 and icefall.         Notice that they are necessary for         hlg_onebest/hlg_rescore decoding and LF-MMI training
2023-07-19 17:18:48,673 INFO Epoch 9 TRAIN info lr 4e-08
2023-07-19 17:18:48,676 INFO using accumulate grad, new batch size is 1 times larger than before
Failed to import k2 and icefall.         Notice that they are necessary for         hlg_onebest/hlg_rescore decoding and LF-MMI training
hpcamp18:126083:126083 [0] NCCL INFO cudaDriverVersion 11060
hpcamp18:126083:126083 [0] NCCL INFO Bootstrap : Using eth4:10.228.64.149<0>
hpcamp18:126083:126083 [0] NCCL INFO NET/Plugin : No plugin found (libnccl-net.so), using internal implementation
hpcamp18:126083:126714 [0] NCCL INFO Failed to open libibverbs.so[.1]
hpcamp18:126083:126714 [0] NCCL INFO NET/Socket : Using [0]eth4:10.228.64.149<0>
hpcamp18:126083:126714 [0] NCCL INFO Using network Socket
hpcamp18:126083:126714 [0] NCCL INFO Setting affinity for GPU 3 to ffff0000,00000000,ffff0000,00000000
hpcamp18:126083:126714 [0] NCCL INFO Trees [0] -1/-1/-1->3->2 [1] -1/-1/-1->3->2
hpcamp18:126083:126714 [0] NCCL INFO Channel 00 : 3[dc000] -> 0[8e000] via SHM/direct/direct
hpcamp18:126083:126714 [0] NCCL INFO Channel 01 : 3[dc000] -> 0[8e000] via SHM/direct/direct
hpcamp18:126083:126714 [0] NCCL INFO Connected all rings
hpcamp18:126083:126714 [0] NCCL INFO Channel 00 : 3[dc000] -> 2[ce000] via SHM/direct/direct
hpcamp18:126083:126714 [0] NCCL INFO Channel 01 : 3[dc000] -> 2[ce000] via SHM/direct/direct
hpcamp18:126083:126714 [0] NCCL INFO Connected all trees
hpcamp18:126083:126714 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
hpcamp18:126083:126714 [0] NCCL INFO 2 coll channels, 2 p2p channels, 2 p2p channels per peer
hpcamp18:126083:126714 [0] NCCL INFO comm 0x4c5a3000 rank 3 nranks 4 cudaDev 0 busId dc000 - Init COMPLETE
hpcamp18:126081:126081 [0] NCCL INFO cudaDriverVersion 11060
hpcamp18:126081:126081 [0] NCCL INFO Bootstrap : Using eth4:10.228.64.149<0>
hpcamp18:126081:126081 [0] NCCL INFO NET/Plugin : No plugin found (libnccl-net.so), using internal implementation
hpcamp18:126081:126720 [0] NCCL INFO Failed to open libibverbs.so[.1]
hpcamp18:126081:126720 [0] NCCL INFO NET/Socket : Using [0]eth4:10.228.64.149<0>
hpcamp18:126081:126720 [0] NCCL INFO Using network Socket
hpcamp18:126081:126720 [0] NCCL INFO Setting affinity for GPU 1 to ffff,00000000,0000ffff,00000000
hpcamp18:126081:126720 [0] NCCL INFO Trees [0] 2/-1/-1->1->0 [1] 2/-1/-1->1->0
hpcamp18:126081:126720 [0] NCCL INFO Channel 00 : 1[9c000] -> 2[ce000] via SHM/direct/direct
hpcamp18:126081:126720 [0] NCCL INFO Channel 01 : 1[9c000] -> 2[ce000] via SHM/direct/direct
hpcamp18:126081:126720 [0] NCCL INFO Connected all rings
hpcamp18:126081:126720 [0] NCCL INFO Channel 00 : 1[9c000] -> 0[8e000] via SHM/direct/direct
hpcamp18:126081:126720 [0] NCCL INFO Channel 01 : 1[9c000] -> 0[8e000] via SHM/direct/direct
hpcamp18:126081:126720 [0] NCCL INFO Connected all trees
hpcamp18:126081:126720 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
hpcamp18:126081:126720 [0] NCCL INFO 2 coll channels, 2 p2p channels, 2 p2p channels per peer
hpcamp18:126081:126720 [0] NCCL INFO comm 0x4b3fcfd0 rank 1 nranks 4 cudaDev 0 busId 9c000 - Init COMPLETE
hpcamp18:126080:126713 [0] NCCL INFO Failed to open libibverbs.so[.1]
hpcamp18:126080:126713 [0] NCCL INFO NET/Socket : Using [0]eth4:10.228.64.149<0>
hpcamp18:126080:126713 [0] NCCL INFO Using network Socket
hpcamp18:126080:126713 [0] NCCL INFO Setting affinity for GPU 0 to ffff,00000000,0000ffff,00000000
hpcamp18:126080:126713 [0] NCCL INFO Channel 00/02 :    0   1   2   3
hpcamp18:126080:126713 [0] NCCL INFO Channel 01/02 :    0   1   2   3
hpcamp18:126080:126713 [0] NCCL INFO Trees [0] 1/-1/-1->0->-1 [1] 1/-1/-1->0->-1
hpcamp18:126080:126713 [0] NCCL INFO Channel 00 : 0[8e000] -> 1[9c000] via SHM/direct/direct
hpcamp18:126080:126713 [0] NCCL INFO Channel 01 : 0[8e000] -> 1[9c000] via SHM/direct/direct
hpcamp18:126080:126713 [0] NCCL INFO Connected all rings
hpcamp18:126080:126713 [0] NCCL INFO Connected all trees
hpcamp18:126080:126713 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
hpcamp18:126080:126713 [0] NCCL INFO 2 coll channels, 2 p2p channels, 2 p2p channels per peer
hpcamp18:126080:126713 [0] NCCL INFO comm 0x48c48bc0 rank 0 nranks 4 cudaDev 0 busId 8e000 - Init COMPLETE
hpcamp18:126082:126082 [0] NCCL INFO cudaDriverVersion 11060
hpcamp18:126082:126082 [0] NCCL INFO Bootstrap : Using eth4:10.228.64.149<0>
hpcamp18:126082:126082 [0] NCCL INFO NET/Plugin : No plugin found (libnccl-net.so), using internal implementation
hpcamp18:126082:126721 [0] NCCL INFO Failed to open libibverbs.so[.1]
hpcamp18:126082:126721 [0] NCCL INFO NET/Socket : Using [0]eth4:10.228.64.149<0>
hpcamp18:126082:126721 [0] NCCL INFO Using network Socket
hpcamp18:126082:126721 [0] NCCL INFO Setting affinity for GPU 2 to ffff0000,00000000,ffff0000,00000000
hpcamp18:126082:126721 [0] NCCL INFO Trees [0] 3/-1/-1->2->1 [1] 3/-1/-1->2->1
hpcamp18:126082:126721 [0] NCCL INFO Channel 00 : 2[ce000] -> 3[dc000] via SHM/direct/direct
hpcamp18:126082:126721 [0] NCCL INFO Channel 01 : 2[ce000] -> 3[dc000] via SHM/direct/direct
hpcamp18:126082:126721 [0] NCCL INFO Connected all rings
hpcamp18:126082:126721 [0] NCCL INFO Channel 00 : 2[ce000] -> 1[9c000] via SHM/direct/direct
hpcamp18:126082:126721 [0] NCCL INFO Channel 01 : 2[ce000] -> 1[9c000] via SHM/direct/direct
hpcamp18:126082:126721 [0] NCCL INFO Connected all trees
hpcamp18:126082:126721 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
hpcamp18:126082:126721 [0] NCCL INFO 2 coll channels, 2 p2p channels, 2 p2p channels per peer
hpcamp18:126082:126721 [0] NCCL INFO comm 0x4cf75110 rank 2 nranks 4 cudaDev 0 busId ce000 - Init COMPLETE
2023-07-19 17:19:02,469 DEBUG TRAIN Batch 9/0 loss 7.916637 loss_att 4.887920 loss_ctc 14.983642 lr 0.00029161 rank 2
2023-07-19 17:19:02,477 DEBUG TRAIN Batch 9/0 loss 10.165641 loss_att 5.771564 loss_ctc 20.418486 lr 0.00029161 rank 3
2023-07-19 17:19:02,599 DEBUG TRAIN Batch 9/0 loss 10.343328 loss_att 6.001000 loss_ctc 20.475424 lr 0.00029161 rank 0
2023-07-19 17:19:02,646 DEBUG TRAIN Batch 9/0 loss 4.972230 loss_att 3.256914 loss_ctc 8.974634 lr 0.00029161 rank 1
